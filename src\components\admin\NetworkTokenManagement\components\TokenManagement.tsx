'use client';

import React, { useState, useMemo } from 'react';
import {
  Coins,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Eye,
  Settings,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Shield,
  ShieldCheck,
  RefreshCw
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { TokenManagementProps, Token } from '../types';
import TokenCard from './TokenCard';
import StatsCards from './StatsCards';
import SearchFilter from './SearchFilter';
import BulkActions from './BulkActions';

export default function TokenManagement({
  tokens,
  pendingTokens,
  networks,
  isLoading,
  error,
  selectedItems,
  searchTerm,
  selectedFilter,
  onTokenToggle,
  onItemAction,
  onSelectionChange,
  onSearchChange,
  onFilterChange,
  hasPendingChanges,
  isRefreshing,
  isSaving
}: TokenManagementProps) {
  const { t, isRTL, getDirectionClasses, formatNumber, formatCurrency } = useAdminTranslation();
  const dirClasses = getDirectionClasses();

  // استخدام البيانات المعلقة إذا كانت متوفرة، وإلا البيانات العادية
  const currentTokens = hasPendingChanges && pendingTokens.length > 0 ? pendingTokens : tokens;

  // تصفية العملات حسب البحث والفلتر
  const filteredTokens = useMemo(() => {
    let filtered = currentTokens;

    // تطبيق البحث
    if (searchTerm) {
      filtered = filtered.filter(token =>
        token.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        token.symbol.toLowerCase().includes(searchTerm.toLowerCase()) ||
        token.address.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // تطبيق الفلتر
    switch (selectedFilter) {
      case 'active':
        filtered = filtered.filter(t => t.status === 'active');
        break;
      case 'inactive':
        filtered = filtered.filter(t => t.status === 'inactive');
        break;
      case 'stablecoins':
        filtered = filtered.filter(t => 
          t.symbol === 'USDT' || t.symbol === 'USDC' || t.symbol === 'DAI' || t.symbol === 'BUSD'
        );
        break;
      case 'verified':
        filtered = filtered.filter(t => t.contractVerified);
        break;
      default:
        // 'all' - لا تطبق أي فلتر
        break;
    }

    return filtered;
  }, [currentTokens, searchTerm, selectedFilter]);

  // إحصائيات العملات
  const tokenStats = useMemo(() => {
    return {
      total: currentTokens.length,
      active: currentTokens.filter(t => t.status === 'active').length,
      inactive: currentTokens.filter(t => t.status === 'inactive').length,
      stablecoins: currentTokens.filter(t => 
        t.symbol === 'USDT' || t.symbol === 'USDC' || t.symbol === 'DAI' || t.symbol === 'BUSD'
      ).length,
      verified: currentTokens.filter(t => t.contractVerified).length,
      pending: currentTokens.filter(t => t.status === 'pending').length,
    };
  }, [currentTokens]);

  // معالجة تحديد/إلغاء تحديد عملة
  const handleTokenSelection = (tokenId: string, selected: boolean) => {
    if (selected) {
      onSelectionChange([...selectedItems, tokenId]);
    } else {
      onSelectionChange(selectedItems.filter(id => id !== tokenId));
    }
  };

  // معالجة تحديد/إلغاء تحديد الكل
  const handleSelectAll = () => {
    if (selectedItems.length === filteredTokens.length) {
      onSelectionChange([]);
    } else {
      onSelectionChange(filteredTokens.map(t => t.id));
    }
  };

  // الحصول على معلومات الشبكة للعملة
  const getNetworkForToken = (networkId: string) => {
    return networks.find(n => n.id === networkId);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center">
          <AlertTriangle className="w-5 h-5 text-red-500 mr-2" />
          <span className="text-red-700 dark:text-red-300">{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* إشعار البيانات المعلقة */}
      {hasPendingChanges && pendingTokens.length > 0 && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <RefreshCw className="w-5 h-5 text-green-500 mr-2" />
              <span className="text-green-700 dark:text-green-300 font-medium">
                {t('tokens.pendingChanges')} ({pendingTokens.length} {t('tokens.tokens')})
              </span>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={() => onItemAction('save')}
                disabled={isSaving}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
              >
                {isSaving ? t('common.saving') : t('common.save')}
              </button>
              <button
                onClick={() => onItemAction('cancel')}
                disabled={isSaving}
                className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700 disabled:opacity-50"
              >
                {t('common.cancel')}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* إحصائيات العملات */}
      <StatsCards stats={tokenStats} type="tokens" isLoading={false} />

      {/* البحث والفلتر */}
      <SearchFilter
        searchTerm={searchTerm}
        selectedFilter={selectedFilter}
        onSearchChange={onSearchChange}
        onFilterChange={onFilterChange}
        type="tokens"
      />

      {/* إجراءات مجمعة */}
      <BulkActions
        selectedCount={selectedItems.length}
        onAction={onItemAction}
        isVisible={selectedItems.length > 0}
        type="tokens"
      />

      {/* جدول العملات */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* رأس الجدول */}
        <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={selectedItems.length === filteredTokens.length && filteredTokens.length > 0}
                onChange={handleSelectAll}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-3 text-sm font-medium text-gray-900 dark:text-white">
                {t('tokens.selectAll')} ({filteredTokens.length})
              </span>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {t('tokens.showing')} {filteredTokens.length} {t('tokens.of')} {currentTokens.length}
            </div>
          </div>
        </div>

        {/* قائمة العملات */}
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredTokens.length > 0 ? (
            filteredTokens.map((token) => (
              <TokenCard
                key={token.id}
                token={token}
                network={getNetworkForToken(token.networkId)}
                isSelected={selectedItems.includes(token.id)}
                onToggle={onTokenToggle}
                onAction={onItemAction}
                onSelectionChange={handleTokenSelection}
              />
            ))
          ) : (
            <div className="px-6 py-12 text-center">
              <Coins className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {t('tokens.noTokens')}
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                {searchTerm || selectedFilter !== 'all' 
                  ? t('tokens.noTokensFiltered')
                  : t('tokens.noTokensFound')
                }
              </p>
            </div>
          )}
        </div>
      </div>

      {/* معلومات إضافية */}
      <div className="text-sm text-gray-500 dark:text-gray-400 text-center">
        {t('tokens.lastUpdated')}: {new Date().toLocaleString()}
      </div>
    </div>
  );
}
