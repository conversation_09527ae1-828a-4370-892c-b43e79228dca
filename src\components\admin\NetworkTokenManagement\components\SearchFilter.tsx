'use client';

import React from 'react';
import { Search, Filter, X } from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { SearchFilterProps } from '../types';

export default function SearchFilter({
  searchTerm,
  selectedFilter,
  onSearchChange,
  onFilterChange,
  type
}: SearchFilterProps) {
  const { t, isRTL } = useAdminTranslation();

  // خيارات الفلتر للشبكات
  const networkFilters = [
    { value: 'all', label: t('networks.filters.all') },
    { value: 'active', label: t('networks.filters.active') },
    { value: 'inactive', label: t('networks.filters.inactive') },
    { value: 'mainnet', label: t('networks.filters.mainnet') },
    { value: 'testnet', label: t('networks.filters.testnet') }
  ];

  // خيارات الفلتر للعملات
  const tokenFilters = [
    { value: 'all', label: t('tokens.filters.all') },
    { value: 'active', label: t('tokens.filters.active') },
    { value: 'inactive', label: t('tokens.filters.inactive') },
    { value: 'stablecoins', label: t('tokens.filters.stablecoins') },
    { value: 'verified', label: t('tokens.filters.verified') }
  ];

  const filters = type === 'networks' ? networkFilters : tokenFilters;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className={`flex flex-col sm:flex-row gap-4 ${isRTL ? 'sm:flex-row-reverse' : ''}`}>
        {/* حقل البحث */}
        <div className="flex-1">
          <div className="relative">
            <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
              <Search className="w-5 h-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              placeholder={
                type === 'networks' 
                  ? t('networks.searchPlaceholder')
                  : t('tokens.searchPlaceholder')
              }
              className={`
                block w-full rounded-lg border border-gray-300 dark:border-gray-600 
                bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                ${isRTL ? 'pr-10 text-right' : 'pl-10 text-left'}
                py-2 focus:border-blue-500 focus:ring-blue-500 
                placeholder-gray-400 dark:placeholder-gray-500
              `}
            />
            {searchTerm && (
              <button
                onClick={() => onSearchChange('')}
                className={`absolute inset-y-0 ${isRTL ? 'left-0 pl-3' : 'right-0 pr-3'} flex items-center`}
              >
                <X className="w-4 h-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
              </button>
            )}
          </div>
        </div>

        {/* فلتر الحالة */}
        <div className="sm:w-48">
          <div className="relative">
            <div className={`absolute inset-y-0 ${isRTL ? 'right-0 pr-3' : 'left-0 pl-3'} flex items-center pointer-events-none`}>
              <Filter className="w-5 h-5 text-gray-400" />
            </div>
            <select
              value={selectedFilter}
              onChange={(e) => onFilterChange(e.target.value)}
              className={`
                block w-full rounded-lg border border-gray-300 dark:border-gray-600 
                bg-white dark:bg-gray-700 text-gray-900 dark:text-white
                ${isRTL ? 'pr-10 text-right' : 'pl-10 text-left'}
                py-2 focus:border-blue-500 focus:ring-blue-500
              `}
            >
              {filters.map((filter) => (
                <option key={filter.value} value={filter.value}>
                  {filter.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* زر مسح الفلاتر */}
        {(searchTerm || selectedFilter !== 'all') && (
          <button
            onClick={() => {
              onSearchChange('');
              onFilterChange('all');
            }}
            className="px-4 py-2 text-sm font-medium text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
          >
            {t('common.clearFilters')}
          </button>
        )}
      </div>

      {/* عرض الفلاتر النشطة */}
      {(searchTerm || selectedFilter !== 'all') && (
        <div className="mt-3 flex flex-wrap gap-2">
          {searchTerm && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
              {t('common.search')}: "{searchTerm}"
              <button
                onClick={() => onSearchChange('')}
                className="ml-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          )}
          {selectedFilter !== 'all' && (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
              {t('common.filter')}: {filters.find(f => f.value === selectedFilter)?.label}
              <button
                onClick={() => onFilterChange('all')}
                className="ml-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
              >
                <X className="w-3 h-3" />
              </button>
            </span>
          )}
        </div>
      )}
    </div>
  );
}
