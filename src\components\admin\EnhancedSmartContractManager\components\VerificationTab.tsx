'use client';

import React from 'react';
import { Search, RefreshCw, Save, AlertTriangle } from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { useContractVerification } from '../hooks';
import { NetworkInfo } from '../types';
import ContractAnalysisDisplay from './ContractAnalysisDisplay';

interface VerificationTabProps {
  supportedNetworks: NetworkInfo[];
  onError: (error: string) => void;
  onSuccess: (message: string) => void;
}

export default function VerificationTab({ 
  supportedNetworks, 
  onError, 
  onSuccess 
}: VerificationTabProps) {
  const { t, isRTL } = useAdminTranslation();

  const {
    contractAddress,
    setContractAddress,
    selectedNetwork,
    setSelectedNetwork,
    verificationResult,
    isAnalyzing,
    isSaving,
    analyzeContract,
    saveToDatabase,
    clearResults
  } = useContractVerification();

  const handleAnalyze = async () => {
    try {
      await analyzeContract();
      onSuccess(t('contracts.messages.analysisSuccess'));
    } catch (error) {
      onError(error instanceof Error ? error.message : t('contracts.messages.analysisFailed'));
    }
  };

  const handleSave = async () => {
    try {
      await saveToDatabase();
      onSuccess(t('contracts.messages.saveSuccess'));
    } catch (error) {
      onError(error instanceof Error ? error.message : t('contracts.messages.saveFailed'));
    }
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${isRTL ? 'text-right' : 'text-left'}`}>
      <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        {t('contracts.verification')}
      </h2>

      {/* Contract Address Input */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('contracts.contractAddress')}
        </label>
        <input
          type="text"
          value={contractAddress}
          onChange={(e) => setContractAddress(e.target.value)}
          placeholder={t('contracts.contractAddressPlaceholder')}
          className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
        />
      </div>

      {/* Network Selection */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          {t('contracts.selectNetwork')}
        </label>
        <select
          value={selectedNetwork}
          onChange={(e) => setSelectedNetwork(e.target.value)}
          className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
        >
          <option value="">{t('contracts.selectNetworkPlaceholder')}</option>
          {supportedNetworks.map((network) => (
            <option key={network.chain_id} value={network.chain_id}>
              {network.name} {network.is_testnet ? `(${t('networks.testnet')})` : ''}
            </option>
          ))}
        </select>
      </div>

      {/* Action Buttons */}
      <div className={`flex gap-3 mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <button
          onClick={handleAnalyze}
          disabled={!contractAddress.trim() || !selectedNetwork || isAnalyzing}
          className={`flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
        >
          {isAnalyzing ? (
            <RefreshCw className="w-4 h-4 animate-spin" />
          ) : (
            <Search className="w-4 h-4" />
          )}
          {isAnalyzing ? t('contracts.analyzing') : t('contracts.analyze')}
        </button>

        {verificationResult && (
          <button
            onClick={handleSave}
            disabled={isSaving}
            className={`flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            {isSaving ? (
              <RefreshCw className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            {isSaving ? t('contracts.saving') : t('contracts.saveToDatabase')}
          </button>
        )}

        {verificationResult && (
          <button
            onClick={clearResults}
            className={`flex items-center gap-2 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            <AlertTriangle className="w-4 h-4" />
            {t('contracts.clearResults')}
          </button>
        )}
      </div>

      {/* Analysis Results */}
      {verificationResult && (
        <ContractAnalysisDisplay 
          result={verificationResult}
          isRTL={isRTL}
        />
      )}
    </div>
  );
}
