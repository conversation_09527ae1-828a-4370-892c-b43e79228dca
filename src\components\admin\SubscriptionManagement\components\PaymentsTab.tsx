'use client';

import React from 'react';
import {
  CreditCard,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  RefreshCw,
  RotateCcw,
  DollarSign,
  Calendar,
  User,
  Receipt,
  ExternalLink
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { PaymentsTabProps } from '../types';

export function PaymentsTab({ transactions, onProcessRefund, onRetryPayment }: PaymentsTabProps) {
  const { t, formatCurrency, formatDate, formatRelativeTime, getDirectionClasses } = useAdminTranslation();
  const dirClasses = getDirectionClasses();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return CheckCircle;
      case 'pending': return Clock;
      case 'failed': return XCircle;
      case 'refunded': return RotateCcw;
      default: return AlertTriangle;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'failed': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'refunded': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method.toLowerCase()) {
      case 'credit card':
      case 'debit card':
        return CreditCard;
      case 'paypal':
        return DollarSign;
      case 'cryptocurrency':
        return Receipt;
      case 'bank transfer':
        return Receipt;
      default:
        return CreditCard;
    }
  };

  // Calculate summary statistics
  const totalAmount = transactions.reduce((sum, t) => sum + t.amount, 0);
  const completedAmount = transactions
    .filter(t => t.status === 'completed')
    .reduce((sum, t) => sum + t.amount, 0);
  const pendingAmount = transactions
    .filter(t => t.status === 'pending')
    .reduce((sum, t) => sum + t.amount, 0);
  const refundedAmount = transactions
    .filter(t => t.status === 'refunded')
    .reduce((sum, t) => sum + t.amount, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          {t('subscriptions.payments.title')}
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {t('subscriptions.payments.description')}
        </p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('subscriptions.payments.totalAmount')}
              </p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                {formatCurrency(totalAmount)}
              </p>
            </div>
            <div className="bg-blue-500 p-3 rounded-lg">
              <DollarSign className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('subscriptions.payments.completedAmount')}
              </p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400 mt-2">
                {formatCurrency(completedAmount)}
              </p>
            </div>
            <div className="bg-green-500 p-3 rounded-lg">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('subscriptions.payments.pendingAmount')}
              </p>
              <p className="text-2xl font-bold text-yellow-600 dark:text-yellow-400 mt-2">
                {formatCurrency(pendingAmount)}
              </p>
            </div>
            <div className="bg-yellow-500 p-3 rounded-lg">
              <Clock className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('subscriptions.payments.refundedAmount')}
              </p>
              <p className="text-2xl font-bold text-gray-600 dark:text-gray-400 mt-2">
                {formatCurrency(refundedAmount)}
              </p>
            </div>
            <div className="bg-gray-500 p-3 rounded-lg">
              <RotateCcw className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className={`px-6 py-3 ${dirClasses.textAlign} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider`}>
                  {t('subscriptions.payments.transaction')}
                </th>
                <th className={`px-6 py-3 ${dirClasses.textAlign} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider`}>
                  {t('subscriptions.payments.user')}
                </th>
                <th className={`px-6 py-3 ${dirClasses.textAlign} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider`}>
                  {t('subscriptions.payments.plan')}
                </th>
                <th className={`px-6 py-3 ${dirClasses.textAlign} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider`}>
                  {t('subscriptions.payments.amount')}
                </th>
                <th className={`px-6 py-3 ${dirClasses.textAlign} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider`}>
                  {t('subscriptions.payments.method')}
                </th>
                <th className={`px-6 py-3 ${dirClasses.textAlign} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider`}>
                  {t('subscriptions.payments.status')}
                </th>
                <th className={`px-6 py-3 ${dirClasses.textAlign} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider`}>
                  {t('subscriptions.payments.date')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {t('common.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {transactions.map((transaction) => {
                const StatusIcon = getStatusIcon(transaction.status);
                const statusColor = getStatusColor(transaction.status);
                const PaymentIcon = getPaymentMethodIcon(transaction.paymentMethod);

                return (
                  <tr key={transaction.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-3 rtl:space-x-reverse">
                        <Receipt className="h-5 w-5 text-gray-400" />
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {transaction.transactionId}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {transaction.id}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <User className="h-4 w-4 text-gray-400" />
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {transaction.username}
                          </div>
                          <div className="text-sm text-gray-500">
                            ID: {transaction.userId}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {transaction.planName}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-bold text-gray-900 dark:text-white">
                        {formatCurrency(transaction.amount)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {transaction.currency}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <PaymentIcon className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-900 dark:text-white">
                          {transaction.paymentMethod}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <StatusIcon className="h-4 w-4" />
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColor}`}>
                          {t(`subscriptions.payments.statuses.${transaction.status}`)}
                        </span>
                      </div>
                      {transaction.failureReason && (
                        <div className="text-xs text-red-500 mt-1">
                          {transaction.failureReason}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white flex items-center space-x-1 rtl:space-x-reverse">
                        <Calendar className="h-3 w-3 text-gray-400" />
                        <span>{formatDate(transaction.createdAt)}</span>
                      </div>
                      {transaction.processedAt && (
                        <div className="text-xs text-gray-500 mt-1">
                          {t('subscriptions.payments.processed')}: {formatRelativeTime(transaction.processedAt)}
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2 rtl:space-x-reverse">
                        {transaction.status === 'failed' && (
                          <button
                            onClick={() => onRetryPayment(transaction.id)}
                            className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 flex items-center space-x-1 rtl:space-x-reverse"
                            title={t('subscriptions.payments.retryPayment')}
                          >
                            <RefreshCw className="h-4 w-4" />
                          </button>
                        )}
                        {transaction.status === 'completed' && (
                          <button
                            onClick={() => onProcessRefund(transaction.id)}
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 flex items-center space-x-1 rtl:space-x-reverse"
                            title={t('subscriptions.payments.processRefund')}
                          >
                            <RotateCcw className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                          title={t('subscriptions.payments.viewDetails')}
                        >
                          <ExternalLink className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Empty State */}
      {transactions.length === 0 && (
        <div className="text-center py-12">
          <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('subscriptions.payments.noTransactions')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {t('subscriptions.payments.noTransactionsDescription')}
          </p>
        </div>
      )}
    </div>
  );
}
