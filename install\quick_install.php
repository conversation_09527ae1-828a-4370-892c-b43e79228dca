<?php
/**
 * تنصيب سريع لمنصة إيكاروس P2P
 * Quick Installation for Ikaros P2P Platform
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

// إعدادات قاعدة البيانات الافتراضية
$config = [
    'db_host' => '127.0.0.1',
    'db_port' => '3306',
    'db_name' => 'ikaros_p2p',
    'db_user' => 'root',
    'db_pass' => '',
];

?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تنصيب سريع - منصة إيكاروس P2P</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }

        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .content {
            padding: 40px;
        }

        .step {
            margin-bottom: 30px;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #3b82f6;
            background: #f8fafc;
            transition: all 0.3s ease;
        }

        .step:hover {
            transform: translateX(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .step h2 {
            color: #1e40af;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .step h2 i {
            width: 30px;
            height: 30px;
            background: #3b82f6;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .success {
            color: #059669;
            background: #ecfdf5;
            border-left-color: #059669;
        }

        .success h2 {
            color: #059669;
        }

        .success h2 i {
            background: #059669;
        }

        .error {
            color: #dc2626;
            background: #fef2f2;
            border-left-color: #dc2626;
        }

        .error h2 {
            color: #dc2626;
        }

        .error h2 i {
            background: #dc2626;
        }

        .warning {
            color: #d97706;
            background: #fffbeb;
            border-left-color: #d97706;
        }

        .warning h2 {
            color: #d97706;
        }

        .warning h2 i {
            background: #d97706;
        }

        .table-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .table-item {
            padding: 10px 15px;
            border-radius: 8px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .table-item.success {
            background: #dcfce7;
            color: #166534;
            border: 1px solid #bbf7d0;
        }

        .table-item.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }

        .login-info {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #0ea5e9;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
        }

        .login-info h3 {
            color: #0c4a6e;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .login-info ul {
            list-style: none;
        }

        .login-info li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .login-info li i {
            color: #0ea5e9;
            width: 20px;
        }

        .btn {
            display: inline-block;
            padding: 15px 30px;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3b82f6, #1d4ed8);
            border-radius: 4px;
            transition: width 0.5s ease;
        }

        .footer {
            text-align: center;
            padding: 30px;
            background: #f8fafc;
            color: #6b7280;
            border-top: 1px solid #e5e7eb;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-rocket"></i> تنصيب منصة إيكاروس P2P</h1>
            <p>نظام تنصيب تلقائي متطور لمنصة التداول اللامركزية</p>
        </div>

        <div class="content">
            <div class="progress-bar">
                <div class="progress-fill" style="width: 0%" id="progressBar"></div>
            </div>

            <div id="installationSteps">
<?php

try {
    // 1. إنشاء قاعدة البيانات
    echo '<div class="step" id="step1">
            <h2><i class="fas fa-database"></i> إنشاء قاعدة البيانات</h2>
            <p>جاري إنشاء قاعدة البيانات وإعدادها...</p>
          </div>';

    $conn = new mysqli($config['db_host'], $config['db_user'], $config['db_pass'], '', $config['db_port']);

    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بـ MySQL: " . $conn->connect_error);
    }

    // حذف قاعدة البيانات إذا كانت موجودة
    $conn->query("DROP DATABASE IF EXISTS `{$config['db_name']}`");

    // إنشاء قاعدة البيانات
    $sql = "CREATE DATABASE `{$config['db_name']}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci";
    if (!$conn->query($sql)) {
        throw new Exception("فشل في إنشاء قاعدة البيانات: " . $conn->error);
    }

    echo '<script>
            document.getElementById("step1").className = "step success";
            document.getElementById("step1").innerHTML = `
                <h2><i class="fas fa-check-circle"></i> إنشاء قاعدة البيانات</h2>
                <p>✅ تم إنشاء قاعدة البيانات <strong>' . $config['db_name'] . '</strong> بنجاح</p>
            `;
            document.getElementById("progressBar").style.width = "25%";
          </script>';
    $conn->close();
    
    // 2. الاتصال بقاعدة البيانات الجديدة
    echo '<div class="step" id="step2">
            <h2><i class="fas fa-cogs"></i> تنفيذ ملف SQL</h2>
            <p>جاري إنشاء الجداول والبيانات الأساسية...</p>
          </div>';

    $conn = new mysqli($config['db_host'], $config['db_user'], $config['db_pass'], $config['db_name'], $config['db_port']);

    if ($conn->connect_error) {
        throw new Exception("فشل الاتصال بقاعدة البيانات: " . $conn->connect_error);
    }

    $conn->set_charset("utf8mb4");

    // تعطيل فحص المفاتيح الخارجية
    $conn->query("SET FOREIGN_KEY_CHECKS = 0");
    $conn->query("SET sql_mode = ''");

    // قراءة ملف SQL
    $sql = file_get_contents('../database/schema.sql');
    if ($sql === false) {
        throw new Exception("فشل في قراءة ملف SQL");
    }

    // تنفيذ الملف كاملاً
    if ($conn->multi_query($sql)) {
        do {
            // تخزين النتيجة الأولى
            if ($result = $conn->store_result()) {
                $result->free();
            }
        } while ($conn->next_result());
    }

    if ($conn->error) {
        echo '<script>
                document.getElementById("step2").className = "step warning";
                document.getElementById("step2").innerHTML += `<p>⚠️ تحذير: ' . addslashes($conn->error) . '</p>`;
              </script>';
    }

    // إعادة تفعيل فحص المفاتيح الخارجية
    $conn->query("SET FOREIGN_KEY_CHECKS = 1");

    echo '<script>
            document.getElementById("step2").className = "step success";
            document.getElementById("step2").innerHTML = `
                <h2><i class="fas fa-check-circle"></i> تنفيذ ملف SQL</h2>
                <p>✅ تم إنشاء جميع الجداول والبيانات الأساسية بنجاح</p>
            `;
            document.getElementById("progressBar").style.width = "50%";
          </script>';
    
    // 3. التحقق من الجداول
    echo '<div class="step" id="step3">
            <h2><i class="fas fa-table"></i> التحقق من الجداول</h2>
            <p>جاري فحص الجداول المنشأة...</p>
          </div>';

    $result = $conn->query("SHOW TABLES");
    $tables = [];
    while ($row = $result->fetch_array()) {
        $tables[] = $row[0];
    }

    $expectedTables = [
        'users', 'offers', 'trades', 'messages', 'reviews', 'notifications',
        'system_settings', 'activity_logs', 'uploaded_files', 'wallet_transactions',
        'wallet_balances', 'wallet_addresses', 'platform_fees', 'user_sessions',
        'user_free_offers', 'user_monthly_limits', 'subscription_plans', 'user_subscriptions'
    ];

    $tablesList = '<div class="table-list">';
    $missingTables = 0;
    foreach ($expectedTables as $table) {
        if (in_array($table, $tables)) {
            $tablesList .= '<div class="table-item success"><i class="fas fa-check"></i> ' . $table . '</div>';
        } else {
            $tablesList .= '<div class="table-item error"><i class="fas fa-times"></i> ' . $table . ' (مفقود)</div>';
            $missingTables++;
        }
    }
    $tablesList .= '</div>';

    $stepClass = $missingTables > 0 ? 'warning' : 'success';
    $icon = $missingTables > 0 ? 'fas fa-exclamation-triangle' : 'fas fa-check-circle';
    $message = $missingTables > 0 ?
        "⚠️ تم إنشاء " . (count($expectedTables) - $missingTables) . " من " . count($expectedTables) . " جداول" :
        "✅ تم إنشاء جميع الجداول بنجاح (" . count($tables) . " جدول)";

    echo '<script>
            document.getElementById("step3").className = "step ' . $stepClass . '";
            document.getElementById("step3").innerHTML = `
                <h2><i class="' . $icon . '"></i> التحقق من الجداول</h2>
                <p>' . $message . '</p>
                ' . addslashes($tablesList) . '
            `;
            document.getElementById("progressBar").style.width = "75%";
          </script>';
    
    // 4. إنشاء ملفات التكوين
    echo '<div class="step" id="step4">
            <h2><i class="fas fa-file-code"></i> إنشاء ملفات التكوين</h2>
            <p>جاري إنشاء ملفات الإعدادات...</p>
          </div>';

    // إنشاء مجلد config
    if (!is_dir('../config')) {
        mkdir('../config', 0755, true);
    }

    // إنشاء ملف config.php
    $configContent = "<?php
return [
    'database' => [
        'host' => '{$config['db_host']}',
        'port' => '{$config['db_port']}',
        'database' => '{$config['db_name']}',
        'username' => '{$config['db_user']}',
        'password' => '{$config['db_pass']}',
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
    ],
    'app' => [
        'name' => 'إيكاروس P2P',
        'url' => 'http://localhost/ikaros-p2p',
        'environment' => 'development',
        'debug' => true,
        'timezone' => 'Asia/Riyadh',
        'locale' => 'ar',
    ],
    'security' => [
        'jwt_secret' => '" . bin2hex(random_bytes(32)) . "',
        'encryption_key' => '" . bin2hex(random_bytes(32)) . "',
        'session_lifetime' => 7200,
    ],
];";

    file_put_contents('../config/config.php', $configContent);

    // إنشاء ملف .env
    $envContent = "DB_HOST={$config['db_host']}
DB_PORT={$config['db_port']}
DB_DATABASE={$config['db_name']}
DB_USERNAME={$config['db_user']}
DB_PASSWORD={$config['db_pass']}

APP_NAME=\"إيكاروس P2P\"
APP_URL=http://localhost/ikaros-p2p
APP_ENV=development
APP_DEBUG=true

NEXT_PUBLIC_ESCROW_CONTRACT_ADDRESS=0x742d35Cc6634C0532925a3b8D4C2C4e0C8b83c8e
NEXT_PUBLIC_USDT_CONTRACT_ADDRESS=0x55d398326f99059fF775485246999027B3197955
NEXT_PUBLIC_NETWORK_ID=97
NEXT_PUBLIC_RPC_URL=https://data-seed-prebsc-1-s1.binance.org:8545/

JWT_SECRET=" . bin2hex(random_bytes(32)) . "
ENCRYPTION_KEY=" . bin2hex(random_bytes(32)) . "
";

    file_put_contents('../.env', $envContent);

    echo '<script>
            document.getElementById("step4").className = "step success";
            document.getElementById("step4").innerHTML = `
                <h2><i class="fas fa-check-circle"></i> إنشاء ملفات التكوين</h2>
                <p>✅ تم إنشاء ملفات التكوين بنجاح</p>
                <ul style="margin-top: 10px; padding-right: 20px;">
                    <li>✅ config/config.php</li>
                    <li>✅ .env</li>
                </ul>
            `;
            document.getElementById("progressBar").style.width = "100%";
          </script>';

    $conn->close();
    
    // رسالة النجاح النهائية
    echo '<div class="step success" style="margin-top: 30px;">
            <h2><i class="fas fa-trophy"></i> تم التنصيب بنجاح!</h2>
            <p>🎉 تهانينا! تم تنصيب منصة إيكاروس P2P بنجاح وهي جاهزة للاستخدام</p>
          </div>';

    echo '<div class="login-info">
            <h3><i class="fas fa-key"></i> بيانات تسجيل الدخول</h3>
            <ul>
                <li><i class="fas fa-user"></i> <strong>اسم المستخدم:</strong> admin</li>
                <li><i class="fas fa-lock"></i> <strong>كلمة المرور:</strong> admin123</li>
                <li><i class="fas fa-envelope"></i> <strong>البريد الإلكتروني:</strong> <EMAIL></li>
            </ul>
          </div>';

    echo '<div style="text-align: center; margin: 30px 0;">
            <a href="../" class="btn">
                <i class="fas fa-rocket"></i> الذهاب إلى المنصة
            </a>
          </div>';

} catch (Exception $e) {
    echo '<div class="step error">
            <h2><i class="fas fa-exclamation-triangle"></i> خطأ في التنصيب</h2>
            <p><strong>تفاصيل الخطأ:</strong></p>
            <p style="background: #fee2e2; padding: 15px; border-radius: 8px; margin: 10px 0; font-family: monospace;">' . htmlspecialchars($e->getMessage()) . '</p>
            <p><strong>الحلول المقترحة:</strong></p>
            <ul style="margin-right: 20px;">
                <li>تأكد من تشغيل خادم MySQL</li>
                <li>تحقق من صحة بيانات الاتصال بقاعدة البيانات</li>
                <li>تأكد من وجود صلاحيات الكتابة في مجلد المشروع</li>
                <li>تحقق من وجود ملف database/schema.sql</li>
            </ul>
            <div style="text-align: center; margin-top: 20px;">
                <a href="javascript:location.reload()" class="btn" style="background: #dc2626;">
                    <i class="fas fa-redo"></i> إعادة المحاولة
                </a>
            </div>
          </div>';
}
?>
            </div>
        </div>

        <div class="footer">
            <p>منصة إيكاروس P2P - نظام التداول اللامركزي المتطور</p>
            <p>تم التطوير بواسطة فريق إيكاروس © 2024</p>
        </div>
    </div>

    <script>
        // إضافة تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تحديث شريط التقدم تدريجياً
            setTimeout(() => {
                const progressBar = document.getElementById('progressBar');
                if (progressBar) {
                    progressBar.style.transition = 'width 0.5s ease';
                }
            }, 100);

            // إضافة تأثير الظهور للخطوات
            const steps = document.querySelectorAll('.step');
            steps.forEach((step, index) => {
                step.style.opacity = '0';
                step.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    step.style.transition = 'all 0.5s ease';
                    step.style.opacity = '1';
                    step.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
