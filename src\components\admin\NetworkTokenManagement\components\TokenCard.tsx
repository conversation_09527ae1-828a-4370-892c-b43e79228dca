'use client';

import React from 'react';
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Eye,
  Settings,
  DollarSign,
  TrendingUp,
  TrendingDown,
  Shield,
  ShieldCheck,
  ExternalLink,
  Copy,
  Power,
  PowerOff,
  Coins,
  Globe
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { TokenCardProps } from '../types';

export default function TokenCard({
  token,
  network,
  isSelected,
  onToggle,
  onAction,
  onSelectionChange
}: TokenCardProps) {
  const { t, isRTL, formatNumber, formatCurrency, formatRelativeTime } = useAdminTranslation();

  // أيقونة الحالة
  const getStatusIcon = () => {
    switch (token.status) {
      case 'active':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'inactive':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'pending':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'error':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      default:
        return <XCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  // لون الحالة
  const getStatusColor = () => {
    switch (token.status) {
      case 'active':
        return 'text-green-600 bg-green-50 dark:bg-green-900/20 dark:text-green-400';
      case 'inactive':
        return 'text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400';
      case 'pending':
        return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'error':
        return 'text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  // تحديد إذا كانت العملة stablecoin
  const isStablecoin = token.symbol === 'USDT' || token.symbol === 'USDC' || token.symbol === 'DAI' || token.symbol === 'BUSD';

  // نسخ العنوان
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // يمكن إضافة toast notification هنا
  };

  // فتح العقد في المستكشف
  const openInExplorer = () => {
    if (network?.explorerUrl && token.address) {
      const explorerUrl = `${network.explorerUrl}/token/${token.address}`;
      window.open(explorerUrl, '_blank');
    }
  };

  return (
    <div className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        {/* معلومات العملة الأساسية */}
        <div className={`flex items-center flex-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {/* Checkbox */}
          <input
            type="checkbox"
            checked={isSelected}
            onChange={(e) => onSelectionChange(token.id, e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />

          {/* أيقونة العملة */}
          <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
            {token.logoUrl ? (
              <img
                src={token.logoUrl}
                alt={token.symbol}
                className="w-8 h-8 rounded-full"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                  e.currentTarget.nextElementSibling?.classList.remove('hidden');
                }}
              />
            ) : null}
            <Coins className={`w-8 h-8 text-blue-500 ${token.logoUrl ? 'hidden' : ''}`} />
          </div>

          {/* تفاصيل العملة */}
          <div className={`flex-1 ${isRTL ? 'mr-4 text-right' : 'ml-4 text-left'}`}>
            <div className="flex items-center gap-2">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                {token.name}
              </h4>
              <span className="text-sm font-mono text-gray-600 dark:text-gray-400">
                {token.symbol}
              </span>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor()}`}>
                {t(`tokens.status.${token.status}`)}
              </span>
              {isStablecoin && (
                <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400">
                  {t('tokens.stablecoin')}
                </span>
              )}
              {token.contractVerified && (
                <ShieldCheck className="w-4 h-4 text-green-500" title={t('tokens.verified')} />
              )}
            </div>

            <div className="mt-1 space-y-1">
              <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                <span className="font-mono text-xs">
                  {token.address.slice(0, 6)}...{token.address.slice(-4)}
                </span>
                {network && (
                  <div className="flex items-center gap-1">
                    <Globe className="w-3 h-3" />
                    <span>{network.name}</span>
                  </div>
                )}
                <span>{t('tokens.decimals')}: {token.decimals}</span>
                <span>{t('tokens.type')}: {t(`tokens.types.${token.type}`)}</span>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-500">
                <div className="flex items-center gap-1">
                  <DollarSign className="w-4 h-4" />
                  <span>{formatCurrency(token.price)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <TrendingUp className="w-4 h-4" />
                  <span>{t('tokens.marketCap')}: {formatCurrency(token.marketCap)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <span>{t('tokens.volume24h')}: {formatCurrency(token.volume24h)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className={`hidden lg:flex items-center gap-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`text-center ${isRTL ? 'text-right' : 'text-left'}`}>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {formatNumber(token.holders)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t('tokens.holders')}
            </div>
          </div>
          
          <div className={`text-center ${isRTL ? 'text-right' : 'text-left'}`}>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {formatNumber(token.transfers24h)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t('tokens.transfers24h')}
            </div>
          </div>
        </div>

        {/* حالة التحقق */}
        <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {token.contractVerified ? (
            <ShieldCheck className="w-5 h-5 text-green-500" />
          ) : (
            <Shield className="w-5 h-5 text-yellow-500" />
          )}
          {getStatusIcon()}
        </div>

        {/* أزرار الإجراءات */}
        <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse mr-4' : 'ml-4'}`}>
          {/* زر التفعيل/الإلغاء */}
          <button
            onClick={() => onToggle(token.id)}
            className={`p-2 rounded-lg transition-colors ${
              token.isEnabled
                ? 'bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400'
            }`}
            title={token.isEnabled ? t('common.disable') : t('common.enable')}
          >
            {token.isEnabled ? (
              <Power className="w-4 h-4" />
            ) : (
              <PowerOff className="w-4 h-4" />
            )}
          </button>

          {/* زر نسخ العنوان */}
          <button
            onClick={() => copyToClipboard(token.address)}
            className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 transition-colors"
            title={t('tokens.copyAddress')}
          >
            <Copy className="w-4 h-4" />
          </button>

          {/* زر فتح في المستكشف */}
          {network?.explorerUrl && (
            <button
              onClick={openInExplorer}
              className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 transition-colors"
              title={t('tokens.openInExplorer')}
            >
              <ExternalLink className="w-4 h-4" />
            </button>
          )}

          {/* زر عرض التفاصيل */}
          <button
            onClick={() => onAction('view', token.id)}
            className="p-2 rounded-lg bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-400 transition-colors"
            title={t('common.viewDetails')}
          >
            <Eye className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* معلومات إضافية للشاشات الصغيرة */}
      <div className="lg:hidden mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500 dark:text-gray-400">{t('tokens.holders')}:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {formatNumber(token.holders)}
            </span>
          </div>
          <div>
            <span className="text-gray-500 dark:text-gray-400">{t('tokens.transfers24h')}:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {formatNumber(token.transfers24h)}
            </span>
          </div>
          <div>
            <span className="text-gray-500 dark:text-gray-400">{t('tokens.price')}:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {formatCurrency(token.price)}
            </span>
          </div>
          <div>
            <span className="text-gray-500 dark:text-gray-400">{t('tokens.lastUpdated')}:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {formatRelativeTime(token.lastUpdated)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
