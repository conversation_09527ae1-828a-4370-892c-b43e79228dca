'use client';

import React from 'react';

interface SubscriptionManagementProps {
  className?: string;
}

// Lazy load the modular component
const SubscriptionManagementModular = React.lazy(() =>
  import('./SubscriptionManagement/index')
);

// Wrapper component for backward compatibility
export default function SubscriptionManagement({ className = '' }: SubscriptionManagementProps) {
  return (
    <React.Suspense fallback={
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    }>
      <SubscriptionManagementModular className={className} />
    </React.Suspense>
  );
}
