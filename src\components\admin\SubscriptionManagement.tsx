'use client';

import React from 'react';

interface SubscriptionManagementProps {
  className?: string;
}

// Wrapper component for backward compatibility
export default function SubscriptionManagement({ className = '' }: SubscriptionManagementProps) {
  // Import the new modular version with absolute path
  const SubscriptionManagementNew = React.lazy(() =>
    import('@/components/admin/SubscriptionManagement/index')
  );

  return (
    <React.Suspense fallback={
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    }>
      <SubscriptionManagementNew className={className} />
    </React.Suspense>
  );
}
