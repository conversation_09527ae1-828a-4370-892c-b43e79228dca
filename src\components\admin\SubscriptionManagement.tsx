'use client';

import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  Users,
  DollarSign,
  TrendingUp,
  Package,
  Shield,
  CheckCircle,
  XCircle,
  Clock,
  Star,
  Crown,
  Zap,
  Building,
  Gift,
  RefreshCw,
  Download,
  Upload,
  Filter,
  Search,
  Plus,
  Edit,
  Trash2,
  Eye,
  Settings,
  BarChart3,
  PieChart,
  LineChart,
  Calendar,
  Mail,
  Phone,
  Smartphone,
  Wallet,
  Key,
  AlertTriangle,
  Info,
  Target,
  Award,
  Percent,
  Activity,
  Globe,
  Lock,
  Unlock,
  PlayCircle,
  PauseCircle,
  StopCircle,
  RotateCcw,
  ArrowUpCircle,
  ArrowDownCircle,
  Copy,
  ExternalLink,
  FileText,
  Calculator,
  Banknote,
  Receipt
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';

interface SubscriptionPlan {
  id: string;
  name: string;
  type: 'free' | 'basic' | 'pro' | 'enterprise';
  price: number;
  currency: string;
  billingCycle: 'monthly' | 'yearly';
  features: {
    offersLimit: number;
    commission: number;
    support: string;
    analytics: boolean;
    priority: number;
    apiAccess: boolean;
    escrowFree: boolean;
    whiteLabel?: boolean;
    dedicatedManager?: boolean;
  };
  isActive: boolean;
  userCount: number;
  revenue: number;
  createdAt: string;
  updatedAt: string;
}

interface UserSubscription {
  id: string;
  userId: string;
  username: string;
  email: string;
  planId: string;
  planName: string;
  status: 'active' | 'expired' | 'cancelled' | 'pending';
  startDate: string;
  endDate: string;
  autoRenew: boolean;
  paymentMethod: string;
  totalPaid: number;
  offersUsed: number;
  offersLimit: number;
  isVerified: boolean;
  verificationLevel: number;
  lastActivity: string;
}

interface PaymentTransaction {
  id: string;
  userId: string;
  username: string;
  planId: string;
  planName: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentMethod: string;
  transactionId: string;
  createdAt: string;
  processedAt?: string;
  failureReason?: string;
}

interface SubscriptionStats {
  totalRevenue: number;
  monthlyRevenue: number;
  activeSubscriptions: number;
  freeUsers: number;
  paidUsers: number;
  conversionRate: number;
  churnRate: number;
  averageRevenue: number;
  lifetimeValue: number;
  monthlyGrowth: number;
}

interface SubscriptionManagementProps {
  className?: string;
}

// Wrapper component for backward compatibility
export default function SubscriptionManagement({ className = '' }: SubscriptionManagementProps) {
  // Import the new modular version
  const SubscriptionManagementNew = React.lazy(() => import('./SubscriptionManagement/index'));
  
  return (
    <React.Suspense fallback={
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    }>
      <SubscriptionManagementNew className={className} />
    </React.Suspense>
  );
}
