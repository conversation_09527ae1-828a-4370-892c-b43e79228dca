'use client';

import React from 'react';
import {
  BarChart3,
  Package,
  Users,
  CreditCard,
  Shield,
  Pie<PERSON>hart,
  Settings
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { useSubscriptionManagement } from './hooks';
import {
  OverviewTab,
  PlansTab,
  SubscriptionsTab,
  PaymentsTab,
  VerificationTab,
  AnalyticsTab,
  SettingsTab
} from './components';
import { SubscriptionManagementProps, TabConfig } from './types';

export default function SubscriptionManagementModular({ className = '' }: SubscriptionManagementProps) {
  const { t, getDirectionClasses } = useAdminTranslation();
  const dirClasses = getDirectionClasses();

  const {
    // Tab state
    activeTab,
    setActiveTab,

    // Filter state
    filterState,
    updateFilter,

    // Modal state
    modalState,
    openPlanModal,
    closePlanModal,
    openSubscriptionModal,
    closeSubscriptionModal,

    // Data
    plans,
    subscriptions,
    filteredSubscriptions,
    transactions,
    stats,

    // Loading and error states
    isLoading,
    hasError,
    errors,

    // Actions
    planActions,
    subscriptionActions,
    transactionActions,
    refreshAll
  } = useSubscriptionManagement();

  // Tab configuration with lazy loading
  const tabs: TabConfig[] = [
    {
      id: 'overview',
      label: t('subscriptions.tabs.overview'),
      icon: BarChart3
    },
    {
      id: 'plans',
      label: t('subscriptions.tabs.plans'),
      icon: Package
    },
    {
      id: 'subscriptions',
      label: t('subscriptions.tabs.subscriptions'),
      icon: Users
    },
    {
      id: 'payments',
      label: t('subscriptions.tabs.payments'),
      icon: CreditCard
    },
    {
      id: 'verification',
      label: t('subscriptions.tabs.verification'),
      icon: Shield
    },
    {
      id: 'analytics',
      label: t('subscriptions.tabs.analytics'),
      icon: PieChart
    },
    {
      id: 'settings',
      label: t('subscriptions.tabs.settings'),
      icon: Settings
    }
  ];

  // Lazy load tab components
  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return React.lazy(() => 
          Promise.resolve({ default: () => <OverviewTab stats={stats} plans={plans} /> })
        );
      case 'plans':
        return React.lazy(() => 
          Promise.resolve({ 
            default: () => (
              <PlansTab 
                plans={plans}
                onCreatePlan={() => openPlanModal()}
                onEditPlan={openPlanModal}
                onDeletePlan={planActions.delete}
              />
            )
          })
        );
      case 'subscriptions':
        return React.lazy(() => 
          Promise.resolve({ 
            default: () => (
              <SubscriptionsTab 
                subscriptions={filteredSubscriptions}
                filterState={filterState}
                onFilterChange={updateFilter}
                onEditSubscription={openSubscriptionModal}
                onCancelSubscription={subscriptionActions.cancel}
              />
            )
          })
        );
      case 'payments':
        return React.lazy(() => 
          Promise.resolve({ 
            default: () => (
              <PaymentsTab 
                transactions={transactions}
                onProcessRefund={transactionActions.processRefund}
                onRetryPayment={transactionActions.retryPayment}
              />
            )
          })
        );
      case 'verification':
        return React.lazy(() => 
          Promise.resolve({ 
            default: () => <VerificationTab subscriptions={subscriptions} />
          })
        );
      case 'analytics':
        return React.lazy(() => 
          Promise.resolve({ 
            default: () => (
              <AnalyticsTab 
                stats={stats}
                plans={plans}
                transactions={transactions}
              />
            )
          })
        );
      case 'settings':
        return React.lazy(() => 
          Promise.resolve({ 
            default: () => (
              <SettingsTab 
                onSaveSettings={(settings) => console.log('Save settings:', settings)}
              />
            )
          })
        );
      default:
        return React.lazy(() => 
          Promise.resolve({ default: () => <OverviewTab stats={stats} plans={plans} /> })
        );
    }
  };

  const TabComponent = renderTabContent();

  if (hasError) {
    return (
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 ${className}`}>
        <div className="text-center">
          <div className="text-red-500 mb-4">
            <svg className="h-12 w-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('subscriptions.error.title')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {t('subscriptions.error.description')}
          </p>
          <button
            onClick={refreshAll}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            {t('common.retry')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {t('subscriptions.title')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t('subscriptions.description')}
            </p>
          </div>
          {isLoading && (
            <div className="flex items-center space-x-2 rtl:space-x-reverse text-blue-600 dark:text-blue-400">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
              <span className="text-sm">{t('common.loading')}</span>
            </div>
          )}
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 rtl:space-x-reverse px-6" aria-label="Tabs">
            {tabs.map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300'
                  } whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 rtl:space-x-reverse transition-colors`}
                >
                  <IconComponent className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          <React.Suspense fallback={
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          }>
            <TabComponent />
          </React.Suspense>
        </div>
      </div>
    </div>
  );
}
