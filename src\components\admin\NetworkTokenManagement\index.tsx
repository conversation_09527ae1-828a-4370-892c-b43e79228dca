'use client';

import React, { useState, useEffect, useCallback, lazy, Suspense } from 'react';
import { Server, Coins, RefreshCw, Save, X, AlertTriangle } from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { useNetworks } from './hooks/useNetworks';
import { useTokens } from './hooks/useTokens';
import { NetworkTokenManagementProps, TabType } from './types';

// Lazy loading للمكونات الفرعية
const NetworkManagement = lazy(() => import('./components/NetworkManagement'));
const TokenManagement = lazy(() => import('./components/TokenManagement'));

// مكون Loading
const LoadingSpinner = () => (
  <div className="flex items-center justify-center py-12">
    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    <span className="ml-3 text-gray-600 dark:text-gray-400">جاري التحميل...</span>
  </div>
);

export default function NetworkTokenManagement({
  className = '',
  isWalletConnected = false,
  walletAddress = '',
  currentNetwork = ''
}: NetworkTokenManagementProps) {
  const { t, isRTL, getDirectionClasses } = useAdminTranslation();
  const dirClasses = getDirectionClasses();

  // حالة التبويبات
  const [activeTab, setActiveTab] = useState<TabType>('networks');
  
  // حالة التحديد والبحث
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  
  // حالة العمليات
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [hasPendingChanges, setHasPendingChanges] = useState(false);

  // استخدام hooks للشبكات والعملات
  const {
    networks,
    pendingNetworks,
    isLoading: networksLoading,
    error: networksError,
    setNetworks,
    setPendingNetworks,
    setError: setNetworksError,
    fetchNetworks,
    toggleNetwork,
    saveNetworksToDatabase,
    getNetworkStats
  } = useNetworks();

  const {
    tokens,
    pendingTokens,
    isLoading: tokensLoading,
    error: tokensError,
    setTokens,
    setPendingTokens,
    setError: setTokensError,
    fetchTokens,
    toggleToken,
    saveTokensToDatabase,
    getTokenStats
  } = useTokens();

  // تحميل البيانات عند بدء التشغيل
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        await Promise.all([
          fetchNetworks(),
          fetchTokens()
        ]);
      } catch (error) {
        console.error('Error loading initial data:', error);
      }
    };

    loadInitialData();
  }, [fetchNetworks, fetchTokens]);

  // معالجة تبديل الشبكة
  const handleNetworkToggle = useCallback(async (networkId: string) => {
    try {
      await toggleNetwork(networkId);
      setHasPendingChanges(true);
      
      // إنشاء نسخة معلقة من الشبكات
      const updatedNetworks = networks.map(network => 
        network.id === networkId 
          ? { ...network, isEnabled: !network.isEnabled, status: (!network.isEnabled ? 'active' : 'inactive') as 'active' | 'inactive' }
          : network
      );
      setPendingNetworks(updatedNetworks);
    } catch (error) {
      console.error('Error toggling network:', error);
    }
  }, [networks, toggleNetwork, setPendingNetworks]);

  // معالجة تبديل العملة
  const handleTokenToggle = useCallback(async (tokenId: string) => {
    try {
      await toggleToken(tokenId);
      setHasPendingChanges(true);
      
      // إنشاء نسخة معلقة من العملات
      const updatedTokens = tokens.map(token => 
        token.id === tokenId 
          ? { ...token, isEnabled: !token.isEnabled, status: (!token.isEnabled ? 'active' : 'inactive') as 'active' | 'inactive' }
          : token
      );
      setPendingTokens(updatedTokens);
    } catch (error) {
      console.error('Error toggling token:', error);
    }
  }, [tokens, toggleToken, setPendingTokens]);

  // معالجة الإجراءات
  const handleItemAction = useCallback(async (action: string, itemId?: string) => {
    try {
      switch (action) {
        case 'refresh':
          setIsRefreshing(true);
          if (activeTab === 'networks') {
            await fetchNetworks(true);
          } else {
            await fetchTokens(true);
          }
          setIsRefreshing(false);
          break;

        case 'save':
          setIsSaving(true);
          if (activeTab === 'networks' && pendingNetworks.length > 0) {
            await saveNetworksToDatabase(pendingNetworks);
            setNetworks(pendingNetworks);
            setPendingNetworks([]);
          } else if (activeTab === 'tokens' && pendingTokens.length > 0) {
            await saveTokensToDatabase(pendingTokens);
            setTokens(pendingTokens);
            setPendingTokens([]);
          }
          setHasPendingChanges(false);
          setIsSaving(false);
          break;

        case 'cancel':
          setPendingNetworks([]);
          setPendingTokens([]);
          setHasPendingChanges(false);
          break;

        case 'enable-selected':
          // تفعيل العناصر المحددة
          if (activeTab === 'networks') {
            const updatedNetworks = networks.map(network => 
              selectedItems.includes(network.id) 
                ? { ...network, isEnabled: true, status: 'active' as const }
                : network
            );
            setPendingNetworks(updatedNetworks);
            setHasPendingChanges(true);
          } else {
            const updatedTokens = tokens.map(token => 
              selectedItems.includes(token.id) 
                ? { ...token, isEnabled: true, status: 'active' as const }
                : token
            );
            setPendingTokens(updatedTokens);
            setHasPendingChanges(true);
          }
          break;

        case 'disable-selected':
          // إلغاء تفعيل العناصر المحددة
          if (activeTab === 'networks') {
            const updatedNetworks = networks.map(network => 
              selectedItems.includes(network.id) 
                ? { ...network, isEnabled: false, status: 'inactive' as const }
                : network
            );
            setPendingNetworks(updatedNetworks);
            setHasPendingChanges(true);
          } else {
            const updatedTokens = tokens.map(token => 
              selectedItems.includes(token.id) 
                ? { ...token, isEnabled: false, status: 'inactive' as const }
                : token
            );
            setPendingTokens(updatedTokens);
            setHasPendingChanges(true);
          }
          break;

        case 'view':
          // عرض تفاصيل العنصر
          console.log('View item:', itemId);
          break;

        default:
          console.log('Unknown action:', action, itemId);
      }
    } catch (error) {
      console.error('Error handling action:', error);
      if (activeTab === 'networks') {
        setNetworksError('فشل في تنفيذ العملية');
      } else {
        setTokensError('فشل في تنفيذ العملية');
      }
    }
  }, [
    activeTab, selectedItems, networks, tokens, pendingNetworks, pendingTokens,
    fetchNetworks, fetchTokens, saveNetworksToDatabase, saveTokensToDatabase,
    setNetworks, setTokens, setPendingNetworks, setPendingTokens,
    setNetworksError, setTokensError
  ]);

  // معالجة تغيير التحديد
  const handleSelectionChange = useCallback((newSelection: string[]) => {
    setSelectedItems(newSelection);
  }, []);

  // معالجة تغيير البحث
  const handleSearchChange = useCallback((term: string) => {
    setSearchTerm(term);
  }, []);

  // معالجة تغيير الفلتر
  const handleFilterChange = useCallback((filter: string) => {
    setSelectedFilter(filter);
  }, []);

  // معالجة تغيير التبويب
  const handleTabChange = (tab: TabType) => {
    setActiveTab(tab);
    setSelectedItems([]);
    setSearchTerm('');
    setSelectedFilter('all');
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* رأس الصفحة */}
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('networkToken.title')}
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            {t('networkToken.description')}
          </p>
        </div>

        {/* أزرار الإجراءات العامة */}
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button
            onClick={() => handleItemAction('refresh')}
            disabled={isRefreshing}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
          >
            <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            {t('common.refresh')}
          </button>
        </div>
      </div>

      {/* التبويبات */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className={`flex space-x-8 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          <button
            onClick={() => handleTabChange('networks')}
            className={`
              py-2 px-1 border-b-2 font-medium text-sm transition-colors
              ${activeTab === 'networks'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }
            `}
          >
            <div className="flex items-center gap-2">
              <Server className="w-4 h-4" />
              {t('networkToken.tabs.networks')}
              <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full text-xs">
                {networks.length}
              </span>
            </div>
          </button>
          
          <button
            onClick={() => handleTabChange('tokens')}
            className={`
              py-2 px-1 border-b-2 font-medium text-sm transition-colors
              ${activeTab === 'tokens'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
              }
            `}
          >
            <div className="flex items-center gap-2">
              <Coins className="w-4 h-4" />
              {t('networkToken.tabs.tokens')}
              <span className="bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full text-xs">
                {tokens.length}
              </span>
            </div>
          </button>
        </nav>
      </div>

      {/* محتوى التبويبات */}
      <Suspense fallback={<LoadingSpinner />}>
        {activeTab === 'networks' ? (
          <NetworkManagement
            networks={networks}
            pendingNetworks={pendingNetworks}
            isLoading={networksLoading}
            error={networksError}
            selectedItems={selectedItems}
            searchTerm={searchTerm}
            selectedFilter={selectedFilter}
            onNetworkToggle={handleNetworkToggle}
            onItemAction={handleItemAction}
            onSelectionChange={handleSelectionChange}
            onSearchChange={handleSearchChange}
            onFilterChange={handleFilterChange}
            hasPendingChanges={hasPendingChanges}
            isRefreshing={isRefreshing}
            isSaving={isSaving}
          />
        ) : (
          <TokenManagement
            tokens={tokens}
            pendingTokens={pendingTokens}
            networks={networks}
            isLoading={tokensLoading}
            error={tokensError}
            selectedItems={selectedItems}
            searchTerm={searchTerm}
            selectedFilter={selectedFilter}
            onTokenToggle={handleTokenToggle}
            onItemAction={handleItemAction}
            onSelectionChange={handleSelectionChange}
            onSearchChange={handleSearchChange}
            onFilterChange={handleFilterChange}
            hasPendingChanges={hasPendingChanges}
            isRefreshing={isRefreshing}
            isSaving={isSaving}
          />
        )}
      </Suspense>
    </div>
  );
}
