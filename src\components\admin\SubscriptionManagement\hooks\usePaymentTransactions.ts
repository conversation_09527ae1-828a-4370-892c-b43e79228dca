import { useState, useEffect } from 'react';
import { PaymentTransaction, UsePaymentTransactionsReturn } from '../types';

export function usePaymentTransactions(): UsePaymentTransactionsReturn {
  const [transactions, setTransactions] = useState<PaymentTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock data - replace with real API calls
  const mockTransactions: PaymentTransaction[] = [
    {
      id: '1',
      userId: '101',
      username: 'john_trader',
      planId: '2',
      planName: 'Basic Plan',
      amount: 9.99,
      currency: 'USD',
      status: 'completed',
      paymentMethod: 'Credit Card',
      transactionId: 'txn_1234567890',
      createdAt: '2024-01-15T10:00:00Z',
      processedAt: '2024-01-15T10:02:00Z'
    },
    {
      id: '2',
      userId: '102',
      username: 'crypto_pro',
      planId: '3',
      planName: 'Pro Plan',
      amount: 24.99,
      currency: 'USD',
      status: 'completed',
      paymentMethod: 'Cryptocurrency',
      transactionId: 'txn_0987654321',
      createdAt: '2024-01-10T14:30:00Z',
      processedAt: '2024-01-10T14:35:00Z'
    },
    {
      id: '3',
      userId: '103',
      username: 'enterprise_corp',
      planId: '4',
      planName: 'Enterprise Plan',
      amount: 99.99,
      currency: 'USD',
      status: 'completed',
      paymentMethod: 'Bank Transfer',
      transactionId: 'txn_1122334455',
      createdAt: '2024-01-01T09:00:00Z',
      processedAt: '2024-01-01T09:05:00Z'
    },
    {
      id: '4',
      userId: '106',
      username: 'pending_user',
      planId: '2',
      planName: 'Basic Plan',
      amount: 9.99,
      currency: 'USD',
      status: 'pending',
      paymentMethod: 'Credit Card',
      transactionId: 'txn_5566778899',
      createdAt: '2024-01-20T16:00:00Z'
    },
    {
      id: '5',
      userId: '107',
      username: 'failed_user',
      planId: '3',
      planName: 'Pro Plan',
      amount: 24.99,
      currency: 'USD',
      status: 'failed',
      paymentMethod: 'Credit Card',
      transactionId: 'txn_9988776655',
      createdAt: '2024-01-18T12:00:00Z',
      failureReason: 'Insufficient funds'
    },
    {
      id: '6',
      userId: '108',
      username: 'refunded_user',
      planId: '2',
      planName: 'Basic Plan',
      amount: 9.99,
      currency: 'USD',
      status: 'refunded',
      paymentMethod: 'PayPal',
      transactionId: 'txn_4433221100',
      createdAt: '2024-01-05T11:00:00Z',
      processedAt: '2024-01-05T11:02:00Z'
    }
  ];

  useEffect(() => {
    loadTransactions();
  }, []);

  const loadTransactions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setTransactions(mockTransactions);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load transactions');
    } finally {
      setLoading(false);
    }
  };

  const processRefund = async (id: string) => {
    try {
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setTransactions(prev => prev.map(transaction => 
        transaction.id === id 
          ? { 
              ...transaction, 
              status: 'refunded' as const,
              processedAt: new Date().toISOString()
            }
          : transaction
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to process refund');
      throw err;
    }
  };

  const retryPayment = async (id: string) => {
    try {
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simulate random success/failure
      const success = Math.random() > 0.3;
      
      setTransactions(prev => prev.map(transaction => 
        transaction.id === id 
          ? { 
              ...transaction, 
              status: success ? 'completed' as const : 'failed' as const,
              processedAt: success ? new Date().toISOString() : undefined,
              failureReason: success ? undefined : 'Payment retry failed'
            }
          : transaction
      ));
      
      if (!success) {
        throw new Error('Payment retry failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to retry payment');
      throw err;
    }
  };

  const refreshTransactions = async () => {
    await loadTransactions();
  };

  return {
    transactions,
    loading,
    error,
    processRefund,
    retryPayment,
    refreshTransactions
  };
}
