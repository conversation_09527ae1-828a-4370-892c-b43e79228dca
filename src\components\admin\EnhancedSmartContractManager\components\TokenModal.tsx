'use client';

import React from 'react';
import { X, Save, Clock, CheckCircle } from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { TokenFormData, NetworkInfo } from '../types';

interface TokenModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  onValidateContract: () => void;
  editingToken: any;
  tokenForm: TokenFormData;
  updateTokenForm: (field: string, value: any) => void;
  supportedNetworks: NetworkInfo[];
  isLoading: boolean;
  isRTL: boolean;
}

export default function TokenModal({
  isOpen,
  onClose,
  onSave,
  onValidateContract,
  editingToken,
  tokenForm,
  updateTokenForm,
  supportedNetworks,
  isLoading,
  isRTL
}: TokenModalProps) {
  const { t } = useAdminTranslation();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {editingToken ? t('tokens.editToken') : t('tokens.addToken')}
          </h3>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-4">
          {/* Network Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('tokens.network')} *
            </label>
            <select
              value={tokenForm.network_id}
              onChange={(e) => updateTokenForm('network_id', e.target.value)}
              className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
            >
              <option value="">{t('tokens.selectNetwork')}</option>
              {supportedNetworks.map((network) => (
                <option key={network.chain_id} value={network.chain_id}>
                  {network.name} {network.is_testnet ? `(${t('networks.testnet')})` : ''}
                </option>
              ))}
            </select>
          </div>

          {/* Contract Address with Validation */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('tokens.contractAddress')} *
            </label>
            <div className={`flex gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <input
                type="text"
                value={tokenForm.token_address}
                onChange={(e) => updateTokenForm('token_address', e.target.value)}
                className={`flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder="0x..."
              />
              <button
                type="button"
                onClick={onValidateContract}
                disabled={!tokenForm.token_address || !tokenForm.network_id || isLoading}
                className={`flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
              >
                {isLoading ? (
                  <Clock className="w-4 h-4 animate-spin" />
                ) : (
                  <CheckCircle className="w-4 h-4" />
                )}
                {t('tokens.validate')}
              </button>
            </div>
          </div>

          {/* Token Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('tokens.symbol')} *
              </label>
              <input
                type="text"
                value={tokenForm.token_symbol}
                onChange={(e) => updateTokenForm('token_symbol', e.target.value)}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder="USDT"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('tokens.decimals')} *
              </label>
              <input
                type="number"
                value={tokenForm.decimals}
                onChange={(e) => updateTokenForm('decimals', e.target.value)}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder="18"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('tokens.name')} *
            </label>
            <input
              type="text"
              value={tokenForm.token_name}
              onChange={(e) => updateTokenForm('token_name', e.target.value)}
              className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
              placeholder="Tether USD"
            />
          </div>

          {/* Trading Limits */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('tokens.minTradeAmount')}
              </label>
              <input
                type="number"
                step="0.01"
                value={tokenForm.min_trade_amount}
                onChange={(e) => updateTokenForm('min_trade_amount', e.target.value)}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder="1.0"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('tokens.maxTradeAmount')}
              </label>
              <input
                type="number"
                step="0.01"
                value={tokenForm.max_trade_amount}
                onChange={(e) => updateTokenForm('max_trade_amount', e.target.value)}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder="100000.0"
              />
            </div>
          </div>

          {/* Platform Fee */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('tokens.platformFeeRate')} (%)
            </label>
            <input
              type="number"
              step="0.001"
              value={parseFloat(tokenForm.platform_fee_rate) * 100}
              onChange={(e) => updateTokenForm('platform_fee_rate', (parseFloat(e.target.value) / 100).toString())}
              className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
              placeholder="0.5"
            />
          </div>

          {/* Checkboxes */}
          <div className={`flex gap-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <label className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <input
                type="checkbox"
                checked={tokenForm.is_stablecoin}
                onChange={(e) => updateTokenForm('is_stablecoin', e.target.checked)}
                className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {t('tokens.isStablecoin')}
              </span>
            </label>

            <label className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <input
                type="checkbox"
                checked={tokenForm.is_active}
                onChange={(e) => updateTokenForm('is_active', e.target.checked)}
                className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {t('tokens.isActive')}
              </span>
            </label>
          </div>
        </div>

        <div className={`flex gap-3 p-6 border-t border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button
            onClick={onSave}
            disabled={isLoading}
            className={`flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            {isLoading ? (
              <Clock className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            {isLoading ? t('common.actions.saving') : (editingToken ? t('common.actions.update') : t('common.actions.add'))}
          </button>

          <button
            onClick={onClose}
            disabled={isLoading}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg font-medium transition-colors"
          >
            {t('common.actions.cancel')}
          </button>
        </div>
      </div>
    </div>
  );
}
