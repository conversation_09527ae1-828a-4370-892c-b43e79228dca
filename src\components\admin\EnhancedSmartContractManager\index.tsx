'use client';

import React, { useEffect } from 'react';
import { Shield, Network, Database, CheckCircle, AlertTriangle } from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { useSmartContractManager } from './hooks';
import { VerificationTab, NetworksTab, TokensTab } from './components';
import { EnhancedSmartContractManagerProps, TabConfig } from './types';

export default function EnhancedSmartContractManagerModular({ 
  className = '' 
}: EnhancedSmartContractManagerProps) {
  const { t, isRTL } = useAdminTranslation();

  const {
    activeTab,
    setActiveTab,
    supportedNetworks,
    isLoading,
    error,
    success,
    loadSupportedNetworks,
    setError,
    setSuccess,
    setIsLoading
  } = useSmartContractManager();

  // Load data based on active tab
  useEffect(() => {
    if (activeTab === 'networks' || activeTab === 'tokens') {
      // Data loading is handled by individual tab components
    }
  }, [activeTab]);

  // Auto-clear messages after 5 seconds
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        setError(null);
        setSuccess(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, success, setError, setSuccess]);

  const tabs: TabConfig[] = [
    {
      id: 'verification',
      label: t('contracts.verification'),
      icon: Shield
    },
    {
      id: 'networks',
      label: t('networks.management'),
      icon: Network
    },
    {
      id: 'tokens',
      label: t('tokens.management'),
      icon: Database
    }
  ];

  return (
    <div className={`space-y-6 ${className} ${isRTL ? 'rtl' : 'ltr'}`}>
      {/* Header */}
      <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${isRTL ? 'text-right' : 'text-left'}`}>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {t('contracts.title')}
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          {t('contracts.description')}
        </p>

        {/* Navigation Tabs */}
        <div className={`flex space-x-1 mt-6 ${isRTL ? 'flex-row-reverse space-x-reverse' : ''}`}>
          {tabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                }`}
              >
                <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <IconComponent className="w-4 h-4" />
                  {tab.label}
                </div>
              </button>
            );
          })}
        </div>
      </div>

      {/* Global Messages */}
      {error && (
        <div className={`bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 ${isRTL ? 'text-right' : 'text-left'}`}>
          <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
            <span className="text-red-800 dark:text-red-300 font-medium">
              {error}
            </span>
          </div>
        </div>
      )}

      {success && (
        <div className={`bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 ${isRTL ? 'text-right' : 'text-left'}`}>
          <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
            <span className="text-green-800 dark:text-green-300 font-medium">
              {success}
            </span>
          </div>
        </div>
      )}

      {/* Tab Content */}
      {activeTab === 'verification' && (
        <VerificationTab
          supportedNetworks={supportedNetworks}
          onError={setError}
          onSuccess={setSuccess}
        />
      )}

      {activeTab === 'networks' && (
        <NetworksTab
          onError={setError}
          onSuccess={setSuccess}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
        />
      )}

      {activeTab === 'tokens' && (
        <TokensTab
          supportedNetworks={supportedNetworks}
          onError={setError}
          onSuccess={setSuccess}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
        />
      )}
    </div>
  );
}
