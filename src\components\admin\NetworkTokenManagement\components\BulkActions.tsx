'use client';

import React from 'react';
import {
  Power,
  PowerOff,
  Trash2,
  RefreshCw,
  Download,
  Upload,
  CheckCircle,
  XCircle,
  Eye
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { BulkActionsProps } from '../types';

export default function BulkActions({
  selectedCount,
  onAction,
  isVisible,
  type
}: BulkActionsProps) {
  const { t, isRTL } = useAdminTranslation();

  if (!isVisible || selectedCount === 0) {
    return null;
  }

  // إجراءات الشبكات
  const networkActions = [
    {
      key: 'enable-selected',
      label: t('networks.actions.enableSelected'),
      icon: Power,
      color: 'bg-green-600 hover:bg-green-700 text-white',
      description: t('networks.actions.enableSelectedDesc')
    },
    {
      key: 'disable-selected',
      label: t('networks.actions.disableSelected'),
      icon: PowerOff,
      color: 'bg-red-600 hover:bg-red-700 text-white',
      description: t('networks.actions.disableSelectedDesc')
    },
    {
      key: 'sync-selected',
      label: t('networks.actions.syncSelected'),
      icon: RefreshCw,
      color: 'bg-blue-600 hover:bg-blue-700 text-white',
      description: t('networks.actions.syncSelectedDesc')
    },
    {
      key: 'export-selected',
      label: t('networks.actions.exportSelected'),
      icon: Download,
      color: 'bg-gray-600 hover:bg-gray-700 text-white',
      description: t('networks.actions.exportSelectedDesc')
    }
  ];

  // إجراءات العملات
  const tokenActions = [
    {
      key: 'enable-selected',
      label: t('tokens.actions.enableSelected'),
      icon: Power,
      color: 'bg-green-600 hover:bg-green-700 text-white',
      description: t('tokens.actions.enableSelectedDesc')
    },
    {
      key: 'disable-selected',
      label: t('tokens.actions.disableSelected'),
      icon: PowerOff,
      color: 'bg-red-600 hover:bg-red-700 text-white',
      description: t('tokens.actions.disableSelectedDesc')
    },
    {
      key: 'verify-selected',
      label: t('tokens.actions.verifySelected'),
      icon: CheckCircle,
      color: 'bg-blue-600 hover:bg-blue-700 text-white',
      description: t('tokens.actions.verifySelectedDesc')
    },
    {
      key: 'refresh-selected',
      label: t('tokens.actions.refreshSelected'),
      icon: RefreshCw,
      color: 'bg-purple-600 hover:bg-purple-700 text-white',
      description: t('tokens.actions.refreshSelectedDesc')
    },
    {
      key: 'export-selected',
      label: t('tokens.actions.exportSelected'),
      icon: Download,
      color: 'bg-gray-600 hover:bg-gray-700 text-white',
      description: t('tokens.actions.exportSelectedDesc')
    }
  ];

  const actions = type === 'networks' ? networkActions : tokenActions;

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        {/* معلومات التحديد */}
        <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
            <CheckCircle className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
            <h3 className="text-sm font-medium text-gray-900 dark:text-white">
              {selectedCount} {type === 'networks' ? t('networks.selected') : t('tokens.selected')}
            </h3>
            <p className="text-xs text-gray-500 dark:text-gray-400">
              {type === 'networks' 
                ? t('networks.bulkActionsDesc') 
                : t('tokens.bulkActionsDesc')
              }
            </p>
          </div>
        </div>

        {/* أزرار الإجراءات */}
        <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {actions.map((action) => (
            <button
              key={action.key}
              onClick={() => onAction(action.key)}
              className={`
                inline-flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg
                transition-colors ${action.color}
              `}
              title={action.description}
            >
              <action.icon className="w-4 h-4" />
              <span className="hidden sm:inline">{action.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* شريط التقدم أو معلومات إضافية */}
      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
          <span>
            {t('common.selectedItems')}: {selectedCount}
          </span>
          <span>
            {t('common.clickToPerformAction')}
          </span>
        </div>
      </div>
    </div>
  );
}
