import { useState, useEffect } from 'react';
import { SubscriptionStats, UseSubscriptionStatsReturn } from '../types';

export function useSubscriptionStats(): UseSubscriptionStatsReturn {
  const [stats, setStats] = useState<SubscriptionStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock statistics data
      const mockStats: SubscriptionStats = {
        totalRevenue: 12194.37,
        monthlyRevenue: 4567.89,
        activeSubscriptions: 545,
        freeUsers: 1250,
        paidUsers: 545,
        conversionRate: 30.4,
        churnRate: 5.2,
        averageRevenue: 22.38,
        lifetimeValue: 134.28,
        monthlyGrowth: 15.7
      };
      
      setStats(mockStats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load statistics');
    } finally {
      setLoading(false);
    }
  };

  const refreshStats = async () => {
    await loadStats();
  };

  return {
    stats,
    loading,
    error,
    refreshStats
  };
}
