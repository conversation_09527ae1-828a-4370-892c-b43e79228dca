'use client';

import React from 'react';
import {
  Server,
  Coins,
  CheckCircle,
  XCircle,
  Clock,
  Globe,
  TestTube,
  Shield,
  DollarSign,
  Activity
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { StatsCardsProps, NetworkStats, TokenStats } from '../types';

export default function StatsCards({ stats, type, isLoading }: StatsCardsProps) {
  const { t, isRTL, formatNumber } = useAdminTranslation();

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="bg-gray-200 dark:bg-gray-700 rounded-lg h-20"></div>
          </div>
        ))}
      </div>
    );
  }

  if (type === 'networks') {
    const networkStats = stats as NetworkStats;
    
    const networkStatsData = [
      {
        label: t('networks.stats.total'),
        value: networkStats.total,
        icon: Server,
        color: 'text-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400',
        bgColor: 'bg-blue-50 dark:bg-blue-900/20'
      },
      {
        label: t('networks.stats.active'),
        value: networkStats.active,
        icon: CheckCircle,
        color: 'text-green-600 bg-green-50 dark:bg-green-900/20 dark:text-green-400',
        bgColor: 'bg-green-50 dark:bg-green-900/20'
      },
      {
        label: t('networks.stats.inactive'),
        value: networkStats.inactive,
        icon: XCircle,
        color: 'text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400',
        bgColor: 'bg-red-50 dark:bg-red-900/20'
      },
      {
        label: t('networks.stats.mainnet'),
        value: networkStats.mainnet,
        icon: Globe,
        color: 'text-purple-600 bg-purple-50 dark:bg-purple-900/20 dark:text-purple-400',
        bgColor: 'bg-purple-50 dark:bg-purple-900/20'
      },
      {
        label: t('networks.stats.testnet'),
        value: networkStats.testnet,
        icon: TestTube,
        color: 'text-orange-600 bg-orange-50 dark:bg-orange-900/20 dark:text-orange-400',
        bgColor: 'bg-orange-50 dark:bg-orange-900/20'
      },
      {
        label: t('networks.stats.syncing'),
        value: networkStats.syncing,
        icon: Activity,
        color: 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20 dark:text-yellow-400',
        bgColor: 'bg-yellow-50 dark:bg-yellow-900/20'
      }
    ];

    return (
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {networkStatsData.map((stat, index) => (
          <div
            key={index}
            className={`${stat.bgColor} rounded-lg p-4 border border-gray-200 dark:border-gray-700`}
          >
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {stat.label}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatNumber(stat.value)}
                </p>
              </div>
              <div className={`p-2 rounded-lg ${stat.color}`}>
                <stat.icon className="w-6 h-6" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (type === 'tokens') {
    const tokenStats = stats as TokenStats;
    
    const tokenStatsData = [
      {
        label: t('tokens.stats.total'),
        value: tokenStats.total,
        icon: Coins,
        color: 'text-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400',
        bgColor: 'bg-blue-50 dark:bg-blue-900/20'
      },
      {
        label: t('tokens.stats.active'),
        value: tokenStats.active,
        icon: CheckCircle,
        color: 'text-green-600 bg-green-50 dark:bg-green-900/20 dark:text-green-400',
        bgColor: 'bg-green-50 dark:bg-green-900/20'
      },
      {
        label: t('tokens.stats.inactive'),
        value: tokenStats.inactive,
        icon: XCircle,
        color: 'text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400',
        bgColor: 'bg-red-50 dark:bg-red-900/20'
      },
      {
        label: t('tokens.stats.stablecoins'),
        value: tokenStats.stablecoins,
        icon: DollarSign,
        color: 'text-green-600 bg-green-50 dark:bg-green-900/20 dark:text-green-400',
        bgColor: 'bg-green-50 dark:bg-green-900/20'
      },
      {
        label: t('tokens.stats.verified'),
        value: tokenStats.verified,
        icon: Shield,
        color: 'text-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400',
        bgColor: 'bg-blue-50 dark:bg-blue-900/20'
      },
      {
        label: t('tokens.stats.pending'),
        value: tokenStats.pending,
        icon: Clock,
        color: 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20 dark:text-yellow-400',
        bgColor: 'bg-yellow-50 dark:bg-yellow-900/20'
      }
    ];

    return (
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {tokenStatsData.map((stat, index) => (
          <div
            key={index}
            className={`${stat.bgColor} rounded-lg p-4 border border-gray-200 dark:border-gray-700`}
          >
            <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
              <div className={`${isRTL ? 'text-right' : 'text-left'}`}>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {stat.label}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                  {formatNumber(stat.value)}
                </p>
              </div>
              <div className={`p-2 rounded-lg ${stat.color}`}>
                <stat.icon className="w-6 h-6" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return null;
}
