/**
 * أنواع البيانات المشتركة لإدارة الشبكات والعملات
 * Shared types for Network and Token Management
 */

export interface Network {
  id: string;
  name: string;
  chainId: number;
  type: 'mainnet' | 'testnet' | 'devnet' | 'private';
  status: 'active' | 'inactive' | 'maintenance' | 'error' | 'syncing';
  rpcUrl: string;
  explorerUrl: string;
  nativeCurrency: {
    name: string;
    symbol: string;
    decimals: number;
  };
  blockTime: number; // in seconds
  gasPrice: number;
  latency: number; // in ms
  uptime: number; // percentage
  blockHeight: number;
  nodeCount: number;
  isEnabled: boolean;
  lastSync: string;
  totalTransactions: number;
  dailyTransactions: number;
}

export interface Token {
  id: string;
  name: string;
  symbol: string;
  address: string;
  networkId: string;
  type: 'native' | 'erc20' | 'erc721' | 'erc1155' | 'bep20' | 'trc20' | 'spl';
  decimals: number;
  totalSupply: number;
  marketCap: number;
  price: number;
  volume24h: number;
  status: 'active' | 'inactive' | 'pending' | 'error';
  isEnabled: boolean;
  logoUrl?: string;
  description?: string;
  website?: string;
  contractVerified: boolean;
  lastUpdated: string;
  holders: number;
  transfers24h: number;
}

export interface NetworkStats {
  total: number;
  active: number;
  inactive: number;
  testnet: number;
  mainnet: number;
  syncing: number;
}

export interface TokenStats {
  total: number;
  active: number;
  inactive: number;
  stablecoins: number;
  verified: number;
  pending: number;
}

export interface NetworkTokenManagementProps {
  className?: string;
  isWalletConnected?: boolean;
  walletAddress?: string;
  currentNetwork?: string;
}

export interface NetworkManagementProps {
  networks: Network[];
  pendingNetworks: Network[];
  isLoading: boolean;
  error: string | null;
  selectedItems: string[];
  searchTerm: string;
  selectedFilter: string;
  onNetworkToggle: (networkId: string) => Promise<void>;
  onItemAction: (action: string, itemId?: string) => Promise<void>;
  onSelectionChange: (selectedIds: string[]) => void;
  onSearchChange: (term: string) => void;
  onFilterChange: (filter: string) => void;
  hasPendingChanges: boolean;
  isRefreshing: boolean;
  isSaving: boolean;
}

export interface TokenManagementProps {
  tokens: Token[];
  pendingTokens: Token[];
  networks: Network[];
  isLoading: boolean;
  error: string | null;
  selectedItems: string[];
  searchTerm: string;
  selectedFilter: string;
  onTokenToggle: (tokenId: string) => Promise<void>;
  onItemAction: (action: string, itemId?: string) => Promise<void>;
  onSelectionChange: (selectedIds: string[]) => void;
  onSearchChange: (term: string) => void;
  onFilterChange: (filter: string) => void;
  hasPendingChanges: boolean;
  isRefreshing: boolean;
  isSaving: boolean;
}

export interface NetworkCardProps {
  network: Network;
  isSelected: boolean;
  onToggle: (networkId: string) => Promise<void>;
  onAction: (action: string, networkId: string) => Promise<void>;
  onSelectionChange: (networkId: string, selected: boolean) => void;
}

export interface TokenCardProps {
  token: Token;
  network?: Network;
  isSelected: boolean;
  onToggle: (tokenId: string) => Promise<void>;
  onAction: (action: string, tokenId: string) => Promise<void>;
  onSelectionChange: (tokenId: string, selected: boolean) => void;
}

export interface BulkActionsProps {
  selectedCount: number;
  onAction: (action: string) => Promise<void>;
  isVisible: boolean;
  type: 'networks' | 'tokens';
}

export interface StatsCardsProps {
  stats: NetworkStats | TokenStats;
  type: 'networks' | 'tokens';
  isLoading: boolean;
}

export interface SearchFilterProps {
  searchTerm: string;
  selectedFilter: string;
  onSearchChange: (term: string) => void;
  onFilterChange: (filter: string) => void;
  type: 'networks' | 'tokens';
}

export interface DetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  item: Network | Token | null;
  type: 'network' | 'token';
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface NetworkApiData {
  networks: Network[];
}

export interface TokenApiData {
  tokens: Token[];
}

// Default data functions
export interface DefaultNetworkData {
  id: string;
  network_name: string;
  chain_id: number;
  rpc_url: string;
  explorer_url: string;
  network_symbol: string;
  is_testnet: boolean;
  is_active: boolean;
  block_time_seconds: number;
  gas_price_gwei: number;
  confirmation_blocks: number;
  daily_transactions: number;
  created_at?: string;
  updated_at?: string;
}

export interface DefaultTokenData {
  id: string;
  token_name: string;
  token_symbol: string;
  token_address: string;
  network_id: string;
  decimals: number;
  is_stablecoin: boolean;
  is_active: boolean;
  market_cap?: number;
  current_price_usd?: number;
  volume_24h?: number;
  total_offers?: number;
  total_trades?: number;
  created_at?: string;
  updated_at?: string;
}

// Utility types
export type FilterType = 'all' | 'active' | 'inactive' | 'testnet' | 'mainnet' | 'stablecoins' | 'verified';
export type ActionType = 'enable' | 'disable' | 'delete' | 'sync' | 'view' | 'edit' | 'refresh';
export type TabType = 'networks' | 'tokens';
