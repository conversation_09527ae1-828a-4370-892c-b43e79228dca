'use client';

import React from 'react';
import {
  Plus,
  Edit,
  Trash2,
  Users,
  DollarSign,
  Crown,
  Gift,
  CreditCard,
  Building,
  CheckCircle,
  XCircle,
  Star,
  Zap
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { PlansTabProps } from '../types';

export function PlansTab({ plans, onCreatePlan, onEditPlan, onDeletePlan }: PlansTabProps) {
  const { t, formatCurrency, formatNumber, getDirectionClasses } = useAdminTranslation();
  const dirClasses = getDirectionClasses();

  const getPlanIcon = (type: string) => {
    switch (type) {
      case 'free': return Gift;
      case 'basic': return CreditCard;
      case 'pro': return Crown;
      case 'enterprise': return Building;
      default: return CreditCard;
    }
  };

  const getPlanColor = (type: string) => {
    switch (type) {
      case 'free': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'basic': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'pro': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'enterprise': return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t('subscriptions.plans.title')}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('subscriptions.plans.description')}
          </p>
        </div>
        <button
          onClick={onCreatePlan}
          className="flex items-center space-x-2 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
        >
          <Plus className="h-4 w-4" />
          <span>{t('subscriptions.plans.createPlan')}</span>
        </button>
      </div>

      {/* Plans Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {plans.map((plan) => {
          const PlanIcon = getPlanIcon(plan.type);
          const planColorClass = getPlanColor(plan.type);

          return (
            <div
              key={plan.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow"
            >
              {/* Plan Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <div className={`p-2 rounded-lg ${planColorClass}`}>
                    <PlanIcon className="h-5 w-5" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {plan.name}
                    </h3>
                    <span className={`text-xs px-2 py-1 rounded-full ${planColorClass}`}>
                      {t(`subscriptions.plans.types.${plan.type}`)}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-1 rtl:space-x-reverse">
                  {plan.isActive ? (
                    <CheckCircle className="h-4 w-4 text-green-500" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-500" />
                  )}
                </div>
              </div>

              {/* Price */}
              <div className="mb-4">
                <div className="flex items-baseline space-x-1 rtl:space-x-reverse">
                  <span className="text-2xl font-bold text-gray-900 dark:text-white">
                    {formatCurrency(plan.price)}
                  </span>
                  <span className="text-sm text-gray-500">
                    /{t(`subscriptions.plans.billingCycles.${plan.billingCycle}`)}
                  </span>
                </div>
              </div>

              {/* Features */}
              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('subscriptions.plans.features.offersLimit')}
                  </span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {plan.features.offersLimit === -1 
                      ? t('subscriptions.plans.unlimited') 
                      : formatNumber(plan.features.offersLimit)
                    }
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('subscriptions.plans.features.commission')}
                  </span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {formatNumber(plan.features.commission)}%
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('subscriptions.plans.features.support')}
                  </span>
                  <span className="font-medium text-gray-900 dark:text-white">
                    {plan.features.support}
                  </span>
                </div>
                {plan.features.analytics && (
                  <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {t('subscriptions.plans.features.analytics')}
                    </span>
                  </div>
                )}
                {plan.features.apiAccess && (
                  <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {t('subscriptions.plans.features.apiAccess')}
                    </span>
                  </div>
                )}
                {plan.features.escrowFree && (
                  <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {t('subscriptions.plans.features.escrowFree')}
                    </span>
                  </div>
                )}
                {plan.features.whiteLabel && (
                  <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm">
                    <Star className="h-3 w-3 text-yellow-500" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {t('subscriptions.plans.features.whiteLabel')}
                    </span>
                  </div>
                )}
                {plan.features.dedicatedManager && (
                  <div className="flex items-center space-x-2 rtl:space-x-reverse text-sm">
                    <Zap className="h-3 w-3 text-purple-500" />
                    <span className="text-gray-600 dark:text-gray-400">
                      {t('subscriptions.plans.features.dedicatedManager')}
                    </span>
                  </div>
                )}
              </div>

              {/* Stats */}
              <div className="grid grid-cols-2 gap-4 mb-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse">
                    <Users className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatNumber(plan.userCount)}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {t('subscriptions.plans.users')}
                  </div>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center space-x-1 rtl:space-x-reverse">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatCurrency(plan.revenue)}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {t('subscriptions.plans.revenue')}
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-2 rtl:space-x-reverse">
                <button
                  onClick={() => onEditPlan(plan)}
                  className="flex-1 flex items-center justify-center space-x-2 rtl:space-x-reverse bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-3 py-2 rounded-lg transition-colors"
                >
                  <Edit className="h-4 w-4" />
                  <span>{t('common.edit')}</span>
                </button>
                <button
                  onClick={() => onDeletePlan(plan.id)}
                  className="flex items-center justify-center bg-red-100 hover:bg-red-200 dark:bg-red-900 dark:hover:bg-red-800 text-red-700 dark:text-red-300 px-3 py-2 rounded-lg transition-colors"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {/* Empty State */}
      {plans.length === 0 && (
        <div className="text-center py-12">
          <Gift className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('subscriptions.plans.noPlans')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {t('subscriptions.plans.noPlansDescription')}
          </p>
          <button
            onClick={onCreatePlan}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            {t('subscriptions.plans.createFirstPlan')}
          </button>
        </div>
      )}
    </div>
  );
}
