'use client';

import React from 'react';
import { Shield, CheckCircle, XCircle, AlertTriangle, Mail, Phone, CreditCard, Award } from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { VerificationTabProps } from '../types';

export function VerificationTab({ subscriptions }: VerificationTabProps) {
  const { t, getDirectionClasses } = useAdminTranslation();
  const dirClasses = getDirectionClasses();

  const verificationLevels = [
    { level: 1, icon: Mail, label: t('subscriptions.verification.basic'), color: 'text-gray-500' },
    { level: 2, icon: Mail, label: t('subscriptions.verification.email'), color: 'text-blue-500' },
    { level: 3, icon: Phone, label: t('subscriptions.verification.phone'), color: 'text-green-500' },
    { level: 4, icon: CreditCard, label: t('subscriptions.verification.identity'), color: 'text-purple-500' },
    { level: 5, icon: Award, label: t('subscriptions.verification.premium'), color: 'text-orange-500' }
  ];

  const verificationStats = verificationLevels.map(level => ({
    ...level,
    count: subscriptions.filter(sub => sub.verificationLevel === level.level).length
  }));

  const verifiedUsers = subscriptions.filter(sub => sub.isVerified).length;
  const unverifiedUsers = subscriptions.filter(sub => !sub.isVerified).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          {t('subscriptions.verification.title')}
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {t('subscriptions.verification.description')}
        </p>
      </div>

      {/* Verification Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('subscriptions.verification.verifiedUsers')}
              </p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400 mt-2">
                {verifiedUsers}
              </p>
            </div>
            <div className="bg-green-500 p-3 rounded-lg">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('subscriptions.verification.unverifiedUsers')}
              </p>
              <p className="text-2xl font-bold text-red-600 dark:text-red-400 mt-2">
                {unverifiedUsers}
              </p>
            </div>
            <div className="bg-red-500 p-3 rounded-lg">
              <XCircle className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Verification Levels */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('subscriptions.verification.levelDistribution')}
        </h3>
        <div className="space-y-4">
          {verificationStats.map((stat) => {
            const IconComponent = stat.icon;
            return (
              <div key={stat.level} className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <IconComponent className={`h-5 w-5 ${stat.color}`} />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {stat.label}
                    </div>
                    <div className="text-sm text-gray-500">
                      {t('subscriptions.verification.level')} {stat.level}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-gray-900 dark:text-white">
                    {stat.count}
                  </div>
                  <div className="text-sm text-gray-500">
                    {subscriptions.length > 0 ? Math.round((stat.count / subscriptions.length) * 100) : 0}%
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Recent Verification Activities */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('subscriptions.verification.recentActivity')}
        </h3>
        <div className="space-y-3">
          {subscriptions.slice(0, 5).map((subscription) => {
            const levelInfo = verificationLevels.find(l => l.level === subscription.verificationLevel);
            const IconComponent = levelInfo?.icon || Shield;
            
            return (
              <div key={subscription.id} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
                <div className="flex items-center space-x-3 rtl:space-x-reverse">
                  <IconComponent className={`h-4 w-4 ${levelInfo?.color || 'text-gray-500'}`} />
                  <div>
                    <div className="font-medium text-gray-900 dark:text-white">
                      {subscription.username}
                    </div>
                    <div className="text-sm text-gray-500">
                      {subscription.email}
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-sm font-medium ${levelInfo?.color || 'text-gray-500'}`}>
                    {levelInfo?.label || t('subscriptions.verification.basic')}
                  </div>
                  <div className="text-xs text-gray-500">
                    {subscription.isVerified ? (
                      <span className="text-green-600">{t('subscriptions.verification.verified')}</span>
                    ) : (
                      <span className="text-red-600">{t('subscriptions.verification.pending')}</span>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
