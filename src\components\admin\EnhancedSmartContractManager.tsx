'use client';

import React from 'react';

interface EnhancedSmartContractManagerProps {
  className?: string;
}

// Lazy load the modular component
const EnhancedSmartContractManagerModular = React.lazy(() =>
  import('./EnhancedSmartContractManager/index')
);

// Wrapper component for backward compatibility
export default function EnhancedSmartContractManager({ className = '' }: EnhancedSmartContractManagerProps) {
  return (
    <React.Suspense fallback={
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    }>
      <EnhancedSmartContractManagerModular className={className} />
    </React.Suspense>
  );
}