import { useState } from 'react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { VerificationResult, ContractVerificationHook } from '../types';

export function useContractVerification(): ContractVerificationHook {
  const { t } = useAdminTranslation();
  
  // State
  const [contractAddress, setContractAddress] = useState('');
  const [selectedNetwork, setSelectedNetwork] = useState('');
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  /**
   * تحليل العقد الذكي
   */
  const analyzeContract = async () => {
    if (!contractAddress.trim() || !selectedNetwork) {
      return;
    }

    setIsAnalyzing(true);

    try {
      const response = await fetch('/api/enhanced-contracts/contract-verification?action=analyze-contract', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contract_address: contractAddress.trim(),
          chain_id: selectedNetwork
        }),
      });

      const data = await response.json();

      if (data.success) {
        setVerificationResult(data.data);
      } else {
        throw new Error(data.error || t('contracts.messages.analysisFailed'));
      }
    } catch (error) {
      console.error('Contract analysis failed:', error);
      throw error;
    } finally {
      setIsAnalyzing(false);
    }
  };

  /**
   * حفظ النتائج في قاعدة البيانات
   */
  const saveToDatabase = async () => {
    if (!contractAddress.trim() || !selectedNetwork) {
      return;
    }

    setIsSaving(true);

    try {
      const response = await fetch('/api/enhanced-contracts/contract-verification?action=auto-populate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contract_address: contractAddress.trim(),
          chain_id: selectedNetwork,
          save_to_database: true
        }),
      });

      const data = await response.json();

      if (data.success) {
        setVerificationResult(data.data.analysis);
      } else {
        throw new Error(data.error || t('contracts.messages.saveFailed'));
      }
    } catch (error) {
      console.error('Save to database failed:', error);
      throw error;
    } finally {
      setIsSaving(false);
    }
  };

  /**
   * مسح النتائج
   */
  const clearResults = () => {
    setVerificationResult(null);
  };

  return {
    // State
    contractAddress,
    setContractAddress,
    selectedNetwork,
    setSelectedNetwork,
    verificationResult,
    setVerificationResult,
    isAnalyzing,
    isSaving,
    
    // Actions
    analyzeContract,
    saveToDatabase,
    clearResults
  };
}
