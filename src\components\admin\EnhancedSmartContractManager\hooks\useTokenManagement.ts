import { useState } from 'react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { TokenFormData, TokenManagementHook } from '../types';

export function useTokenManagement(): TokenManagementHook {
  const { t } = useAdminTranslation();
  
  // State
  const [tokensList, setTokensList] = useState<any[]>([]);
  const [showTokenModal, setShowTokenModal] = useState(false);
  const [editingToken, setEditingToken] = useState<any>(null);
  const [tokenForm, setTokenForm] = useState<TokenFormData>({
    network_id: '',
    token_address: '',
    token_symbol: '',
    token_name: '',
    decimals: '18',
    is_stablecoin: false,
    is_active: true,
    min_trade_amount: '1.0',
    max_trade_amount: '100000.0',
    platform_fee_rate: '0.005'
  });

  /**
   * تحميل قائمة العملات
   */
  const loadTokensList = async () => {
    try {
      const response = await fetch('/api/enhanced-contracts/token-management?action=list');
      const data = await response.json();
      
      if (data.success) {
        setTokensList(data.data || []);
      } else {
        throw new Error(data.error || t('tokens.loadFailed'));
      }
    } catch (error) {
      console.error('Failed to load tokens:', error);
      throw error;
    }
  };

  /**
   * إضافة عملة جديدة
   */
  const handleAddToken = async () => {
    try {
      const response = await fetch('/api/enhanced-contracts/token-management?action=add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(tokenForm)
      });

      const data = await response.json();
      if (data.success) {
        setShowTokenModal(false);
        resetTokenForm();
        await loadTokensList();
      } else {
        throw new Error(data.error || t('tokens.addFailed'));
      }
    } catch (error) {
      console.error('Failed to add token:', error);
      throw error;
    }
  };

  /**
   * تحديث عملة موجودة
   */
  const handleUpdateToken = async () => {
    if (!editingToken) return;

    try {
      const response = await fetch(`/api/enhanced-contracts/token-management?action=update&id=${editingToken.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(tokenForm)
      });

      const data = await response.json();
      if (data.success) {
        setShowTokenModal(false);
        setEditingToken(null);
        resetTokenForm();
        await loadTokensList();
      } else {
        throw new Error(data.error || t('tokens.updateFailed'));
      }
    } catch (error) {
      console.error('Failed to update token:', error);
      throw error;
    }
  };

  /**
   * حذف عملة
   */
  const handleDeleteToken = async (id: number) => {
    try {
      const response = await fetch(`/api/enhanced-contracts/token-management?action=delete&id=${id}`, {
        method: 'DELETE'
      });

      const data = await response.json();
      if (data.success) {
        await loadTokensList();
      } else {
        throw new Error(data.error || t('tokens.deleteFailed'));
      }
    } catch (error) {
      console.error('Failed to delete token:', error);
      throw error;
    }
  };

  /**
   * فتح مودال العملة
   */
  const openTokenModal = () => {
    setShowTokenModal(true);
  };

  /**
   * إغلاق مودال العملة
   */
  const closeTokenModal = () => {
    setShowTokenModal(false);
    setEditingToken(null);
    resetTokenForm();
  };

  /**
   * تعديل عملة
   */
  const editToken = (token: any) => {
    setEditingToken(token);
    setTokenForm({
      network_id: token.network_id?.toString() || '',
      token_address: token.token_address || '',
      token_symbol: token.token_symbol || '',
      token_name: token.token_name || '',
      decimals: token.decimals?.toString() || '18',
      is_stablecoin: token.is_stablecoin || false,
      is_active: token.is_active !== false,
      min_trade_amount: token.min_trade_amount?.toString() || '1.0',
      max_trade_amount: token.max_trade_amount?.toString() || '100000.0',
      platform_fee_rate: token.platform_fee_rate?.toString() || '0.005'
    });
    setShowTokenModal(true);
  };

  /**
   * إعادة تعيين نموذج العملة
   */
  const resetTokenForm = () => {
    setTokenForm({
      network_id: '',
      token_address: '',
      token_symbol: '',
      token_name: '',
      decimals: '18',
      is_stablecoin: false,
      is_active: true,
      min_trade_amount: '1.0',
      max_trade_amount: '100000.0',
      platform_fee_rate: '0.005'
    });
  };

  /**
   * تحديث حقل في نموذج العملة
   */
  const updateTokenForm = (field: string, value: any) => {
    setTokenForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  /**
   * التحقق من عقد العملة
   */
  const validateTokenContract = async () => {
    if (!tokenForm.token_address || !tokenForm.network_id) {
      throw new Error(t('tokens.contractAddressRequired'));
    }

    try {
      const response = await fetch(`/api/enhanced-contracts/token-management?action=fetch-token-info&contract_address=${tokenForm.token_address}&network_id=${tokenForm.network_id}`);
      const data = await response.json();

      if (data.success) {
        const tokenInfo = data.data.token_info;
        setTokenForm(prev => ({
          ...prev,
          token_symbol: tokenInfo.symbol || prev.token_symbol,
          token_name: tokenInfo.name || prev.token_name,
          decimals: tokenInfo.decimals?.toString() || prev.decimals,
          is_stablecoin: data.data.suggested_settings?.is_stablecoin || prev.is_stablecoin
        }));
      } else {
        throw new Error(data.error || t('tokens.contractValidationFailed'));
      }
    } catch (error) {
      console.error('Token contract validation failed:', error);
      throw error;
    }
  };

  return {
    // State
    tokensList,
    showTokenModal,
    editingToken,
    tokenForm,
    
    // Actions
    loadTokensList,
    handleAddToken,
    handleUpdateToken,
    handleDeleteToken,
    openTokenModal,
    closeTokenModal,
    editToken,
    resetTokenForm,
    updateTokenForm,
    validateTokenContract
  };
}
