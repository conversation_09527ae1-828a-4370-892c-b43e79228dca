// Types for Enhanced Smart Contract Manager System

export interface NetworkInfo {
  chain_id: number;
  name: string;
  explorer_url: string;
  native_currency: string;
  is_testnet?: boolean;
  is_active?: boolean;
}

export interface ContractInfo {
  address: string;
  name: string;
  source_code: string;
  abi: string;
  compiler_version: string;
  optimization_used: string;
  verified: boolean;
}

export interface ContractType {
  is_token: boolean;
  is_nft: boolean;
  is_escrow: boolean;
  is_defi: boolean;
  is_proxy: boolean;
  contract_type: string;
  functions: string[];
  events: string[];
}

export interface SecurityAnalysis {
  is_verified: boolean;
  has_proxy: boolean;
  optimization_used: boolean;
  security_issues: string[];
  security_score: number;
}

export interface SupportedToken {
  contract_address: string;
  name: string;
  symbol: string;
  decimals: number;
  is_stablecoin: boolean;
  is_verified: boolean;
}

export interface VerificationResult {
  verified: boolean;
  contract_info: ContractInfo;
  contract_type: ContractType;
  security_analysis: SecurityAnalysis;
  supported_tokens: SupportedToken[];
  network_info: NetworkInfo;
  recommendations: string[];
}

export interface NetworkFormData {
  name: string;
  chain_id: string;
  rpc_url: string;
  explorer_url: string;
  native_currency: string;
  currency_symbol: string;
  currency_decimals: string;
  is_testnet: boolean;
  is_active: boolean;
  gas_price_gwei: string;
  block_time_seconds: string;
  confirmation_blocks: string;
}

export interface TokenFormData {
  network_id: string;
  token_address: string;
  token_symbol: string;
  token_name: string;
  decimals: string;
  is_stablecoin: boolean;
  is_active: boolean;
  min_trade_amount: string;
  max_trade_amount: string;
  platform_fee_rate: string;
}

export interface EnhancedSmartContractManagerProps {
  className?: string;
}

export interface TabConfig {
  id: string;
  label: string;
  icon: any;
}

// Hook return types
export interface ContractVerificationHook {
  // State
  contractAddress: string;
  setContractAddress: (address: string) => void;
  selectedNetwork: string;
  setSelectedNetwork: (network: string) => void;
  verificationResult: VerificationResult | null;
  setVerificationResult: (result: VerificationResult | null) => void;
  isAnalyzing: boolean;
  isSaving: boolean;
  
  // Actions
  analyzeContract: () => Promise<void>;
  saveToDatabase: () => Promise<void>;
  clearResults: () => void;
}

export interface NetworkManagementHook {
  // State
  networksList: any[];
  showNetworkModal: boolean;
  editingNetwork: any;
  networkForm: NetworkFormData;
  
  // Actions
  loadNetworksList: () => Promise<void>;
  handleAddNetwork: () => Promise<void>;
  handleUpdateNetwork: () => Promise<void>;
  handleDeleteNetwork: (id: number) => Promise<void>;
  openNetworkModal: () => void;
  closeNetworkModal: () => void;
  editNetwork: (network: any) => void;
  resetNetworkForm: () => void;
  updateNetworkForm: (field: string, value: any) => void;
}

export interface TokenManagementHook {
  // State
  tokensList: any[];
  showTokenModal: boolean;
  editingToken: any;
  tokenForm: TokenFormData;
  
  // Actions
  loadTokensList: () => Promise<void>;
  handleAddToken: () => Promise<void>;
  handleUpdateToken: () => Promise<void>;
  handleDeleteToken: (id: number) => Promise<void>;
  openTokenModal: () => void;
  closeTokenModal: () => void;
  editToken: (token: any) => void;
  resetTokenForm: () => void;
  updateTokenForm: (field: string, value: any) => void;
  validateTokenContract: () => Promise<void>;
}

export interface SmartContractManagerHook {
  // Tab state
  activeTab: string;
  setActiveTab: (tab: string) => void;
  
  // Global state
  supportedNetworks: NetworkInfo[];
  isLoading: boolean;
  error: string | null;
  success: string | null;
  
  // Actions
  loadSupportedNetworks: () => Promise<void>;
  setError: (error: string | null) => void;
  setSuccess: (success: string | null) => void;
  setIsLoading: (loading: boolean) => void;
}
