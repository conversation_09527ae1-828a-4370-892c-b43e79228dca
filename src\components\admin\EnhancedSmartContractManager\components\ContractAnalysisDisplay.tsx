'use client';

import React from 'react';
import { 
  CheckCircle, 
  AlertTriangle, 
  Shield, 
  Code, 
  ExternalLink,
  Star,
  Activity,
  Database
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { VerificationResult } from '../types';

interface ContractAnalysisDisplayProps {
  result: VerificationResult;
  isRTL: boolean;
}

export default function ContractAnalysisDisplay({ result, isRTL }: ContractAnalysisDisplayProps) {
  const { t } = useAdminTranslation();

  const getSecurityColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400';
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-red-600 dark:text-red-400';
  };

  const getSecurityBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 dark:bg-green-900/30';
    if (score >= 60) return 'bg-yellow-100 dark:bg-yellow-900/30';
    return 'bg-red-100 dark:bg-red-900/30';
  };

  return (
    <div className="space-y-6">
      {/* Contract Info */}
      <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
        <h3 className={`text-lg font-semibold text-gray-900 dark:text-white mb-3 ${isRTL ? 'flex items-center gap-2 flex-row-reverse' : 'flex items-center gap-2'}`}>
          <Code className="w-5 h-5" />
          {t('contracts.contractInfo')}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              {t('contracts.address')}:
            </span>
            <p className="text-sm text-gray-900 dark:text-white font-mono break-all">
              {result.contract_info.address}
            </p>
          </div>
          
          <div>
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              {t('contracts.name')}:
            </span>
            <p className="text-sm text-gray-900 dark:text-white">
              {result.contract_info.name || t('contracts.notAvailable')}
            </p>
          </div>
          
          <div>
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              {t('contracts.verified')}:
            </span>
            <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
              {result.contract_info.verified ? (
                <>
                  <CheckCircle className="w-4 h-4 text-green-600" />
                  <span className="text-sm text-green-600 dark:text-green-400">
                    {t('contracts.verified')}
                  </span>
                </>
              ) : (
                <>
                  <AlertTriangle className="w-4 h-4 text-red-600" />
                  <span className="text-sm text-red-600 dark:text-red-400">
                    {t('contracts.notVerified')}
                  </span>
                </>
              )}
            </div>
          </div>
          
          <div>
            <span className="text-sm font-medium text-gray-600 dark:text-gray-400">
              {t('contracts.network')}:
            </span>
            <p className="text-sm text-gray-900 dark:text-white">
              {result.network_info.name}
            </p>
          </div>
        </div>
      </div>

      {/* Contract Type */}
      <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
        <h3 className={`text-lg font-semibold text-gray-900 dark:text-white mb-3 ${isRTL ? 'flex items-center gap-2 flex-row-reverse' : 'flex items-center gap-2'}`}>
          <Activity className="w-5 h-5" />
          {t('contracts.contractType')}
        </h3>
        
        <div className={`flex flex-wrap gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {result.contract_type.is_token && (
            <span className="px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded-full text-sm">
              {t('contracts.types.token')}
            </span>
          )}
          {result.contract_type.is_nft && (
            <span className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full text-sm">
              {t('contracts.types.nft')}
            </span>
          )}
          {result.contract_type.is_escrow && (
            <span className="px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-sm">
              {t('contracts.types.escrow')}
            </span>
          )}
          {result.contract_type.is_defi && (
            <span className="px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded-full text-sm">
              {t('contracts.types.defi')}
            </span>
          )}
          {result.contract_type.is_proxy && (
            <span className="px-3 py-1 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-full text-sm">
              {t('contracts.types.proxy')}
            </span>
          )}
        </div>
        
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
          {t('contracts.primaryType')}: <span className="font-medium">{result.contract_type.contract_type}</span>
        </p>
      </div>

      {/* Security Analysis */}
      <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
        <h3 className={`text-lg font-semibold text-gray-900 dark:text-white mb-3 ${isRTL ? 'flex items-center gap-2 flex-row-reverse' : 'flex items-center gap-2'}`}>
          <Shield className="w-5 h-5" />
          {t('contracts.securityAnalysis')}
        </h3>
        
        <div className={`flex items-center gap-3 mb-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`flex items-center gap-2 px-3 py-2 rounded-lg ${getSecurityBgColor(result.security_analysis.security_score)} ${isRTL ? 'flex-row-reverse' : ''}`}>
            <Star className={`w-5 h-5 ${getSecurityColor(result.security_analysis.security_score)}`} />
            <span className={`font-semibold ${getSecurityColor(result.security_analysis.security_score)}`}>
              {result.security_analysis.security_score}/100
            </span>
          </div>
          <span className="text-sm text-gray-600 dark:text-gray-400">
            {t('contracts.securityScore')}
          </span>
        </div>
        
        {result.security_analysis.security_issues.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('contracts.securityIssues')}:
            </h4>
            <ul className={`space-y-1 ${isRTL ? 'text-right' : 'text-left'}`}>
              {result.security_analysis.security_issues.map((issue, index) => (
                <li key={index} className={`flex items-start gap-2 text-sm text-red-600 dark:text-red-400 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <AlertTriangle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                  <span>{issue}</span>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Supported Tokens */}
      {result.supported_tokens.length > 0 && (
        <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4">
          <h3 className={`text-lg font-semibold text-gray-900 dark:text-white mb-3 ${isRTL ? 'flex items-center gap-2 flex-row-reverse' : 'flex items-center gap-2'}`}>
            <Database className="w-5 h-5" />
            {t('contracts.supportedTokens')}
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {result.supported_tokens.map((token, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {token.symbol}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {token.name}
                    </p>
                  </div>
                  {token.is_stablecoin && (
                    <span className="px-2 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded text-xs">
                      {t('tokens.stablecoin')}
                    </span>
                  )}
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 font-mono break-all">
                  {token.contract_address}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recommendations */}
      {result.recommendations.length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
          <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-300 mb-3">
            {t('contracts.recommendations')}
          </h3>
          
          <ul className={`space-y-2 ${isRTL ? 'text-right' : 'text-left'}`}>
            {result.recommendations.map((recommendation, index) => (
              <li key={index} className={`flex items-start gap-2 text-sm text-blue-800 dark:text-blue-300 ${isRTL ? 'flex-row-reverse' : ''}`}>
                <CheckCircle className="w-4 h-4 mt-0.5 flex-shrink-0" />
                <span>{recommendation}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Explorer Link */}
      {result.network_info.explorer_url && (
        <div className={`flex justify-center ${isRTL ? 'flex-row-reverse' : ''}`}>
          <a
            href={`${result.network_info.explorer_url}/address/${result.contract_info.address}`}
            target="_blank"
            rel="noopener noreferrer"
            className={`flex items-center gap-2 px-4 py-2 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            <ExternalLink className="w-4 h-4" />
            {t('contracts.viewOnExplorer')}
          </a>
        </div>
      )}
    </div>
  );
}
