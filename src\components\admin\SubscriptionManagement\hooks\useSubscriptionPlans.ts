import { useState, useEffect } from 'react';
import { SubscriptionPlan, UseSubscriptionPlansReturn } from '../types';

export function useSubscriptionPlans(): UseSubscriptionPlansReturn {
  const [plans, setPlans] = useState<SubscriptionPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock data - replace with real API calls
  const mockPlans: SubscriptionPlan[] = [
    {
      id: '1',
      name: 'Free Plan',
      type: 'free',
      price: 0,
      currency: 'USD',
      billingCycle: 'monthly',
      features: {
        offersLimit: 3,
        commission: 2.0,
        support: 'Basic',
        analytics: false,
        priority: 1,
        apiAccess: false,
        escrowFree: false
      },
      isActive: true,
      userCount: 1250,
      revenue: 0,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: 'Basic Plan',
      type: 'basic',
      price: 9.99,
      currency: 'USD',
      billingCycle: 'monthly',
      features: {
        offersLimit: 15,
        commission: 1.5,
        support: 'Advanced',
        analytics: true,
        priority: 2,
        apiAccess: false,
        escrowFree: false
      },
      isActive: true,
      userCount: 320,
      revenue: 3196.8,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-15T10:30:00Z'
    },
    {
      id: '3',
      name: 'Pro Plan',
      type: 'pro',
      price: 24.99,
      currency: 'USD',
      billingCycle: 'monthly',
      features: {
        offersLimit: 50,
        commission: 1.0,
        support: 'Dedicated',
        analytics: true,
        priority: 3,
        apiAccess: true,
        escrowFree: true
      },
      isActive: true,
      userCount: 180,
      revenue: 4498.2,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-20T14:15:00Z'
    },
    {
      id: '4',
      name: 'Enterprise Plan',
      type: 'enterprise',
      price: 99.99,
      currency: 'USD',
      billingCycle: 'monthly',
      features: {
        offersLimit: -1, // Unlimited
        commission: 0.5,
        support: '24/7',
        analytics: true,
        priority: 4,
        apiAccess: true,
        escrowFree: true,
        whiteLabel: true,
        dedicatedManager: true
      },
      isActive: true,
      userCount: 45,
      revenue: 4499.55,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-18T09:45:00Z'
    }
  ];

  useEffect(() => {
    loadPlans();
  }, []);

  const loadPlans = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setPlans(mockPlans);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load plans');
    } finally {
      setLoading(false);
    }
  };

  const createPlan = async (planData: Partial<SubscriptionPlan>) => {
    try {
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newPlan: SubscriptionPlan = {
        id: Date.now().toString(),
        name: planData.name || '',
        type: planData.type || 'basic',
        price: planData.price || 0,
        currency: planData.currency || 'USD',
        billingCycle: planData.billingCycle || 'monthly',
        features: planData.features || {
          offersLimit: 10,
          commission: 1.5,
          support: 'Basic',
          analytics: false,
          priority: 1,
          apiAccess: false,
          escrowFree: false
        },
        isActive: true,
        userCount: 0,
        revenue: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      setPlans(prev => [...prev, newPlan]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create plan');
      throw err;
    }
  };

  const updatePlan = async (id: string, planData: Partial<SubscriptionPlan>) => {
    try {
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setPlans(prev => prev.map(plan => 
        plan.id === id 
          ? { ...plan, ...planData, updatedAt: new Date().toISOString() }
          : plan
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update plan');
      throw err;
    }
  };

  const deletePlan = async (id: string) => {
    try {
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setPlans(prev => prev.filter(plan => plan.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete plan');
      throw err;
    }
  };

  const refreshPlans = async () => {
    await loadPlans();
  };

  return {
    plans,
    loading,
    error,
    createPlan,
    updatePlan,
    deletePlan,
    refreshPlans
  };
}
