'use client';

import React from 'react';
import { X, Save, Clock } from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { NetworkFormData } from '../types';

interface NetworkModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
  editingNetwork: any;
  networkForm: NetworkFormData;
  updateNetworkForm: (field: string, value: any) => void;
  isLoading: boolean;
  isRTL: boolean;
}

export default function NetworkModal({
  isOpen,
  onClose,
  onSave,
  editingNetwork,
  networkForm,
  updateNetworkForm,
  isLoading,
  isRTL
}: NetworkModalProps) {
  const { t } = useAdminTranslation();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className={`flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {editingNetwork ? t('networks.editNetwork') : t('networks.addNetwork')}
          </h3>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        <div className="p-6 space-y-4">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('networks.name')} *
              </label>
              <input
                type="text"
                value={networkForm.name}
                onChange={(e) => updateNetworkForm('name', e.target.value)}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder={t('networks.namePlaceholder')}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('networks.chainId')} *
              </label>
              <input
                type="number"
                value={networkForm.chain_id}
                onChange={(e) => updateNetworkForm('chain_id', e.target.value)}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder="1"
              />
            </div>
          </div>

          {/* URLs */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('networks.rpcUrl')} *
            </label>
            <input
              type="url"
              value={networkForm.rpc_url}
              onChange={(e) => updateNetworkForm('rpc_url', e.target.value)}
              className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
              placeholder="https://mainnet.infura.io/v3/..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('networks.explorerUrl')} *
            </label>
            <input
              type="url"
              value={networkForm.explorer_url}
              onChange={(e) => updateNetworkForm('explorer_url', e.target.value)}
              className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
              placeholder="https://etherscan.io"
            />
          </div>

          {/* Currency Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('networks.nativeCurrency')} *
              </label>
              <input
                type="text"
                value={networkForm.native_currency}
                onChange={(e) => updateNetworkForm('native_currency', e.target.value)}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder="Ethereum"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('networks.currencySymbol')} *
              </label>
              <input
                type="text"
                value={networkForm.currency_symbol}
                onChange={(e) => updateNetworkForm('currency_symbol', e.target.value)}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder="ETH"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('networks.currencyDecimals')}
              </label>
              <input
                type="number"
                value={networkForm.currency_decimals}
                onChange={(e) => updateNetworkForm('currency_decimals', e.target.value)}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder="18"
              />
            </div>
          </div>

          {/* Advanced Settings */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('networks.gasPriceGwei')}
              </label>
              <input
                type="number"
                step="0.1"
                value={networkForm.gas_price_gwei}
                onChange={(e) => updateNetworkForm('gas_price_gwei', e.target.value)}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder="20"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('networks.blockTimeSeconds')}
              </label>
              <input
                type="number"
                value={networkForm.block_time_seconds}
                onChange={(e) => updateNetworkForm('block_time_seconds', e.target.value)}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder="15"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {t('networks.confirmationBlocks')}
              </label>
              <input
                type="number"
                value={networkForm.confirmation_blocks}
                onChange={(e) => updateNetworkForm('confirmation_blocks', e.target.value)}
                className={`w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white ${isRTL ? 'text-right' : 'text-left'}`}
                placeholder="12"
              />
            </div>
          </div>

          {/* Checkboxes */}
          <div className={`flex gap-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
            <label className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <input
                type="checkbox"
                checked={networkForm.is_testnet}
                onChange={(e) => updateNetworkForm('is_testnet', e.target.checked)}
                className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {t('networks.isTestnet')}
              </span>
            </label>

            <label className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
              <input
                type="checkbox"
                checked={networkForm.is_active}
                onChange={(e) => updateNetworkForm('is_active', e.target.checked)}
                className="rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {t('networks.isActive')}
              </span>
            </label>
          </div>
        </div>

        <div className={`flex gap-3 p-6 border-t border-gray-200 dark:border-gray-700 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <button
            onClick={onSave}
            disabled={isLoading}
            className={`flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
          >
            {isLoading ? (
              <Clock className="w-4 h-4 animate-spin" />
            ) : (
              <Save className="w-4 h-4" />
            )}
            {isLoading ? t('common.actions.saving') : (editingNetwork ? t('common.actions.update') : t('common.actions.add'))}
          </button>

          <button
            onClick={onClose}
            disabled={isLoading}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200 border border-gray-300 dark:border-gray-600 rounded-lg font-medium transition-colors"
          >
            {t('common.actions.cancel')}
          </button>
        </div>
      </div>
    </div>
  );
}
