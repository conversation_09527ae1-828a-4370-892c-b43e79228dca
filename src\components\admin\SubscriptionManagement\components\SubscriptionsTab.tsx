'use client';

import React from 'react';
import {
  Search,
  Filter,
  Edit,
  Eye,
  XCircle,
  CheckCircle,
  Clock,
  AlertTriangle,
  Mail,
  Calendar,
  CreditCard,
  Shield,
  Activity
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { SubscriptionsTabProps } from '../types';

export function SubscriptionsTab({ 
  subscriptions, 
  filterState, 
  onFilterChange, 
  onEditSubscription, 
  onCancelSubscription 
}: SubscriptionsTabProps) {
  const { t, formatCurrency, formatDate, formatRelativeTime, getDirectionClasses } = useAdminTranslation();
  const dirClasses = getDirectionClasses();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return CheckCircle;
      case 'expired': return Clock;
      case 'cancelled': return XCircle;
      case 'pending': return AlertTriangle;
      default: return Clock;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'expired': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'cancelled': return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getVerificationLevel = (level: number) => {
    const levels = [
      { min: 1, max: 1, label: t('subscriptions.verification.basic'), color: 'text-gray-500' },
      { min: 2, max: 2, label: t('subscriptions.verification.email'), color: 'text-blue-500' },
      { min: 3, max: 3, label: t('subscriptions.verification.phone'), color: 'text-green-500' },
      { min: 4, max: 4, label: t('subscriptions.verification.identity'), color: 'text-purple-500' },
      { min: 5, max: 5, label: t('subscriptions.verification.premium'), color: 'text-orange-500' }
    ];
    
    const levelInfo = levels.find(l => level >= l.min && level <= l.max) || levels[0];
    return levelInfo;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t('subscriptions.subscriptions.title')}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('subscriptions.subscriptions.description')}
          </p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className={`absolute top-3 ${dirClasses.start} h-4 w-4 text-gray-400`} />
            <input
              type="text"
              placeholder={t('subscriptions.subscriptions.searchPlaceholder')}
              value={filterState.searchTerm}
              onChange={(e) => onFilterChange({ searchTerm: e.target.value })}
              className={`w-full ${dirClasses.paddingStart} pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            />
          </div>

          {/* Status Filter */}
          <div className="relative">
            <Filter className={`absolute top-3 ${dirClasses.start} h-4 w-4 text-gray-400`} />
            <select
              value={filterState.filterStatus}
              onChange={(e) => onFilterChange({ filterStatus: e.target.value })}
              className={`w-full ${dirClasses.paddingStart} pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            >
              <option value="all">{t('subscriptions.subscriptions.allStatuses')}</option>
              <option value="active">{t('subscriptions.subscriptions.statuses.active')}</option>
              <option value="expired">{t('subscriptions.subscriptions.statuses.expired')}</option>
              <option value="cancelled">{t('subscriptions.subscriptions.statuses.cancelled')}</option>
              <option value="pending">{t('subscriptions.subscriptions.statuses.pending')}</option>
            </select>
          </div>

          {/* Plan Filter */}
          <div className="relative">
            <CreditCard className={`absolute top-3 ${dirClasses.start} h-4 w-4 text-gray-400`} />
            <select
              value={filterState.filterPlan}
              onChange={(e) => onFilterChange({ filterPlan: e.target.value })}
              className={`w-full ${dirClasses.paddingStart} pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white`}
            >
              <option value="all">{t('subscriptions.subscriptions.allPlans')}</option>
              <option value="1">{t('subscriptions.plans.types.free')}</option>
              <option value="2">{t('subscriptions.plans.types.basic')}</option>
              <option value="3">{t('subscriptions.plans.types.pro')}</option>
              <option value="4">{t('subscriptions.plans.types.enterprise')}</option>
            </select>
          </div>
        </div>
      </div>

      {/* Subscriptions Table */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className={`px-6 py-3 ${dirClasses.textAlign} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider`}>
                  {t('subscriptions.subscriptions.user')}
                </th>
                <th className={`px-6 py-3 ${dirClasses.textAlign} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider`}>
                  {t('subscriptions.subscriptions.plan')}
                </th>
                <th className={`px-6 py-3 ${dirClasses.textAlign} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider`}>
                  {t('subscriptions.subscriptions.status')}
                </th>
                <th className={`px-6 py-3 ${dirClasses.textAlign} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider`}>
                  {t('subscriptions.subscriptions.usage')}
                </th>
                <th className={`px-6 py-3 ${dirClasses.textAlign} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider`}>
                  {t('subscriptions.subscriptions.verification')}
                </th>
                <th className={`px-6 py-3 ${dirClasses.textAlign} text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider`}>
                  {t('subscriptions.subscriptions.lastActivity')}
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {t('common.actions')}
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {subscriptions.map((subscription) => {
                const StatusIcon = getStatusIcon(subscription.status);
                const statusColor = getStatusColor(subscription.status);
                const verificationInfo = getVerificationLevel(subscription.verificationLevel);
                const usagePercentage = subscription.offersLimit === -1 
                  ? 0 
                  : (subscription.offersUsed / subscription.offersLimit) * 100;

                return (
                  <tr key={subscription.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {subscription.username}
                          </div>
                          <div className="text-sm text-gray-500 flex items-center space-x-1 rtl:space-x-reverse">
                            <Mail className="h-3 w-3" />
                            <span>{subscription.email}</span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {subscription.planName}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatCurrency(subscription.totalPaid)} {t('subscriptions.subscriptions.paid')}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <StatusIcon className="h-4 w-4" />
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${statusColor}`}>
                          {t(`subscriptions.subscriptions.statuses.${subscription.status}`)}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 mt-1 flex items-center space-x-1 rtl:space-x-reverse">
                        <Calendar className="h-3 w-3" />
                        <span>{formatDate(subscription.endDate)}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        {subscription.offersUsed} / {subscription.offersLimit === -1 ? '∞' : subscription.offersLimit}
                      </div>
                      {subscription.offersLimit !== -1 && (
                        <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-1">
                          <div 
                            className={`h-2 rounded-full ${
                              usagePercentage >= 90 ? 'bg-red-500' : 
                              usagePercentage >= 70 ? 'bg-yellow-500' : 'bg-green-500'
                            }`}
                            style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                          ></div>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2 rtl:space-x-reverse">
                        <Shield className={`h-4 w-4 ${verificationInfo.color}`} />
                        <span className={`text-sm ${verificationInfo.color}`}>
                          {verificationInfo.label}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white flex items-center space-x-1 rtl:space-x-reverse">
                        <Activity className="h-3 w-3 text-gray-400" />
                        <span>{formatRelativeTime(subscription.lastActivity)}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2 rtl:space-x-reverse">
                        <button
                          onClick={() => onEditSubscription(subscription)}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => onEditSubscription(subscription)}
                          className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        {subscription.status === 'active' && (
                          <button
                            onClick={() => onCancelSubscription(subscription.id)}
                            className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          >
                            <XCircle className="h-4 w-4" />
                          </button>
                        )}
                      </div>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Empty State */}
      {subscriptions.length === 0 && (
        <div className="text-center py-12">
          <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {t('subscriptions.subscriptions.noSubscriptions')}
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            {t('subscriptions.subscriptions.noSubscriptionsDescription')}
          </p>
        </div>
      )}
    </div>
  );
}
