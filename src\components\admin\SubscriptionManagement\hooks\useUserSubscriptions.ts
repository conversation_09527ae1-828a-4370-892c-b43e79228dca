import { useState, useEffect } from 'react';
import { UserSubscription, UseUserSubscriptionsReturn } from '../types';

export function useUserSubscriptions(): UseUserSubscriptionsReturn {
  const [subscriptions, setSubscriptions] = useState<UserSubscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Mock data - replace with real API calls
  const mockSubscriptions: UserSubscription[] = [
    {
      id: '1',
      userId: '101',
      username: 'john_trader',
      email: '<EMAIL>',
      planId: '2',
      planName: 'Basic Plan',
      status: 'active',
      startDate: '2024-01-15T00:00:00Z',
      endDate: '2024-02-15T00:00:00Z',
      autoRenew: true,
      paymentMethod: 'Credit Card',
      totalPaid: 9.99,
      offersUsed: 8,
      offersLimit: 15,
      isVerified: true,
      verificationLevel: 3,
      lastActivity: '2024-01-20T10:30:00Z'
    },
    {
      id: '2',
      userId: '102',
      username: 'crypto_pro',
      email: '<EMAIL>',
      planId: '3',
      planName: 'Pro Plan',
      status: 'active',
      startDate: '2024-01-10T00:00:00Z',
      endDate: '2024-02-10T00:00:00Z',
      autoRenew: true,
      paymentMethod: 'Cryptocurrency',
      totalPaid: 24.99,
      offersUsed: 32,
      offersLimit: 50,
      isVerified: true,
      verificationLevel: 4,
      lastActivity: '2024-01-20T15:45:00Z'
    },
    {
      id: '3',
      userId: '103',
      username: 'enterprise_corp',
      email: '<EMAIL>',
      planId: '4',
      planName: 'Enterprise Plan',
      status: 'active',
      startDate: '2024-01-01T00:00:00Z',
      endDate: '2024-02-01T00:00:00Z',
      autoRenew: true,
      paymentMethod: 'Bank Transfer',
      totalPaid: 99.99,
      offersUsed: 125,
      offersLimit: -1,
      isVerified: true,
      verificationLevel: 5,
      lastActivity: '2024-01-20T16:20:00Z'
    },
    {
      id: '4',
      userId: '104',
      username: 'free_user',
      email: '<EMAIL>',
      planId: '1',
      planName: 'Free Plan',
      status: 'active',
      startDate: '2024-01-01T00:00:00Z',
      endDate: '2024-12-31T23:59:59Z',
      autoRenew: false,
      paymentMethod: 'Free',
      totalPaid: 0,
      offersUsed: 2,
      offersLimit: 3,
      isVerified: false,
      verificationLevel: 1,
      lastActivity: '2024-01-19T09:20:00Z'
    },
    {
      id: '5',
      userId: '105',
      username: 'expired_user',
      email: '<EMAIL>',
      planId: '2',
      planName: 'Basic Plan',
      status: 'expired',
      startDate: '2023-12-15T00:00:00Z',
      endDate: '2024-01-15T00:00:00Z',
      autoRenew: false,
      paymentMethod: 'Credit Card',
      totalPaid: 9.99,
      offersUsed: 15,
      offersLimit: 15,
      isVerified: true,
      verificationLevel: 2,
      lastActivity: '2024-01-14T18:45:00Z'
    }
  ];

  useEffect(() => {
    loadSubscriptions();
  }, []);

  const loadSubscriptions = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setSubscriptions(mockSubscriptions);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load subscriptions');
    } finally {
      setLoading(false);
    }
  };

  const updateSubscription = async (id: string, subscriptionData: Partial<UserSubscription>) => {
    try {
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSubscriptions(prev => prev.map(subscription => 
        subscription.id === id 
          ? { ...subscription, ...subscriptionData }
          : subscription
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update subscription');
      throw err;
    }
  };

  const cancelSubscription = async (id: string) => {
    try {
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSubscriptions(prev => prev.map(subscription => 
        subscription.id === id 
          ? { ...subscription, status: 'cancelled' as const, autoRenew: false }
          : subscription
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cancel subscription');
      throw err;
    }
  };

  const renewSubscription = async (id: string) => {
    try {
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const now = new Date();
      const endDate = new Date(now);
      endDate.setMonth(endDate.getMonth() + 1);
      
      setSubscriptions(prev => prev.map(subscription => 
        subscription.id === id 
          ? { 
              ...subscription, 
              status: 'active' as const, 
              startDate: now.toISOString(),
              endDate: endDate.toISOString(),
              autoRenew: true 
            }
          : subscription
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to renew subscription');
      throw err;
    }
  };

  const refreshSubscriptions = async () => {
    await loadSubscriptions();
  };

  return {
    subscriptions,
    loading,
    error,
    updateSubscription,
    cancelSubscription,
    renewSubscription,
    refreshSubscriptions
  };
}
