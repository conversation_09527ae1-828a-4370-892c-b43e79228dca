'use client';

import React, { useEffect } from 'react';
import { Database, Plus, Edit, Trash2, Check<PERSON><PERSON>cle, AlertTriangle, Star } from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { useTokenManagement } from '../hooks';
import { NetworkInfo } from '../types';
import TokenModal from './TokenModal';

interface TokensTabProps {
  supportedNetworks: NetworkInfo[];
  onError: (error: string) => void;
  onSuccess: (message: string) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

export default function TokensTab({ 
  supportedNetworks,
  onError, 
  onSuccess, 
  isLoading, 
  setIsLoading 
}: TokensTabProps) {
  const { t, isRTL } = useAdminTranslation();

  const {
    tokensList,
    showTokenModal,
    editingToken,
    tokenForm,
    loadTokensList,
    handleAddToken,
    handleUpdateToken,
    handleDeleteToken,
    openTokenModal,
    closeTokenModal,
    editToken,
    updateTokenForm,
    validateTokenContract
  } = useTokenManagement();

  // Load tokens on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        await loadTokensList();
      } catch (error) {
        onError(error instanceof Error ? error.message : t('tokens.loadFailed'));
      } finally {
        setIsLoading(false);
      }
    };
    
    loadData();
  }, []);

  const handleAdd = async () => {
    try {
      setIsLoading(true);
      await handleAddToken();
      onSuccess(t('tokens.addSuccess'));
    } catch (error) {
      onError(error instanceof Error ? error.message : t('tokens.addFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdate = async () => {
    try {
      setIsLoading(true);
      await handleUpdateToken();
      onSuccess(t('tokens.updateSuccess'));
    } catch (error) {
      onError(error instanceof Error ? error.message : t('tokens.updateFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm(t('tokens.confirmDelete'))) return;
    
    try {
      setIsLoading(true);
      await handleDeleteToken(id);
      onSuccess(t('tokens.deleteSuccess'));
    } catch (error) {
      onError(error instanceof Error ? error.message : t('tokens.deleteFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleValidateContract = async () => {
    try {
      setIsLoading(true);
      await validateTokenContract();
      onSuccess(t('tokens.contractValidated'));
    } catch (error) {
      onError(error instanceof Error ? error.message : t('tokens.contractValidationFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const getNetworkName = (networkId: number) => {
    const network = supportedNetworks.find(n => n.chain_id === networkId);
    return network ? network.name : `Chain ID: ${networkId}`;
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${isRTL ? 'text-right' : 'text-left'}`}>
      <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          {t('tokens.management')}
        </h2>
        <button
          onClick={openTokenModal}
          disabled={isLoading}
          className={`flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
        >
          <Plus className="w-4 h-4" />
          {t('tokens.addToken')}
        </button>
      </div>

      {/* Tokens List */}
      <div className="space-y-4">
        {tokensList.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            {isLoading ? t('common.loading') : t('tokens.noTokens')}
          </div>
        ) : (
          tokensList.map((token) => (
            <div
              key={token.id}
              className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
            >
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Database className="w-5 h-5 text-green-600 dark:text-green-400" />
                    <div>
                      <h3 className="font-semibold text-gray-900 dark:text-white">
                        {token.token_symbol}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {token.token_name}
                      </p>
                    </div>
                  </div>
                  
                  <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    {token.is_stablecoin && (
                      <span className="px-2 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 rounded text-xs">
                        <Star className="w-3 h-3 inline mr-1" />
                        {t('tokens.stablecoin')}
                      </span>
                    )}
                    
                    <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      {token.is_active ? (
                        <CheckCircle className="w-3 h-3 text-green-600" />
                      ) : (
                        <AlertTriangle className="w-3 h-3 text-red-600" />
                      )}
                      <span className={`text-xs ${token.is_active ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                        {token.is_active ? t('tokens.active') : t('tokens.inactive')}
                      </span>
                    </div>
                  </div>
                </div>

                <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <button
                    onClick={() => editToken(token)}
                    disabled={isLoading}
                    className="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                    title={t('common.actions.edit')}
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  
                  <button
                    onClick={() => handleDelete(token.id)}
                    disabled={isLoading}
                    className="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                    title={t('common.actions.delete')}
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div className="mt-3 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('tokens.network')}:
                  </span>
                  <span className="ml-2 text-gray-900 dark:text-white">
                    {getNetworkName(token.network_id)}
                  </span>
                </div>
                
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('tokens.decimals')}:
                  </span>
                  <span className="ml-2 font-mono text-gray-900 dark:text-white">
                    {token.decimals}
                  </span>
                </div>
                
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('tokens.minAmount')}:
                  </span>
                  <span className="ml-2 text-gray-900 dark:text-white">
                    {token.min_trade_amount}
                  </span>
                </div>
                
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('tokens.maxAmount')}:
                  </span>
                  <span className="ml-2 text-gray-900 dark:text-white">
                    {token.max_trade_amount}
                  </span>
                </div>
              </div>

              <div className="mt-2 text-sm">
                <span className="text-gray-600 dark:text-gray-400">
                  {t('tokens.contractAddress')}:
                </span>
                <span className="ml-2 font-mono text-gray-900 dark:text-white text-xs break-all">
                  {token.token_address}
                </span>
              </div>

              {token.platform_fee_rate && (
                <div className="mt-2 text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('tokens.platformFee')}:
                  </span>
                  <span className="ml-2 text-gray-900 dark:text-white">
                    {(parseFloat(token.platform_fee_rate) * 100).toFixed(2)}%
                  </span>
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Token Modal */}
      {showTokenModal && (
        <TokenModal
          isOpen={showTokenModal}
          onClose={closeTokenModal}
          onSave={editingToken ? handleUpdate : handleAdd}
          onValidateContract={handleValidateContract}
          editingToken={editingToken}
          tokenForm={tokenForm}
          updateTokenForm={updateTokenForm}
          supportedNetworks={supportedNetworks}
          isLoading={isLoading}
          isRTL={isRTL}
        />
      )}
    </div>
  );
}
