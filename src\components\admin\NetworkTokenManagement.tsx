'use client';

/**
 * مكون إدارة الشبكات والعملات - النسخة المحسنة
 * Network and Token Management Component - Optimized Version
 *
 * هذا المكون يستخدم النسخة الجديدة المقسمة من NetworkTokenManagement
 * This component uses the new split version of NetworkTokenManagement
 */

import React from 'react';
import NetworkTokenManagementNew from './NetworkTokenManagement';

interface NetworkTokenManagementProps {
  className?: string;
  isWalletConnected?: boolean;
  walletAddress?: string;
  currentNetwork?: string;
}

export default function NetworkTokenManagement(props: NetworkTokenManagementProps) {
  return <NetworkTokenManagementNew {...props} />;
}
