import { useState } from 'react';
import { FilterState, ModalState, SubscriptionPlan, UserSubscription } from '../types';
import { useSubscriptionPlans } from './useSubscriptionPlans';
import { useUserSubscriptions } from './useUserSubscriptions';
import { usePaymentTransactions } from './usePaymentTransactions';
import { useSubscriptionStats } from './useSubscriptionStats';

export function useSubscriptionManagement() {
  // Tab management
  const [activeTab, setActiveTab] = useState('overview');

  // Filter state
  const [filterState, setFilterState] = useState<FilterState>({
    searchTerm: '',
    filterStatus: 'all',
    filterPlan: 'all'
  });

  // Modal state
  const [modalState, setModalState] = useState<ModalState>({
    showPlanModal: false,
    showSubscriptionModal: false,
    selectedPlan: null,
    selectedSubscription: null
  });

  // Data hooks
  const plansHook = useSubscriptionPlans();
  const subscriptionsHook = useUserSubscriptions();
  const transactionsHook = usePaymentTransactions();
  const statsHook = useSubscriptionStats();

  // Filter handlers
  const updateFilter = (updates: Partial<FilterState>) => {
    setFilterState(prev => ({ ...prev, ...updates }));
  };

  const resetFilters = () => {
    setFilterState({
      searchTerm: '',
      filterStatus: 'all',
      filterPlan: 'all'
    });
  };

  // Modal handlers
  const openPlanModal = (plan?: SubscriptionPlan) => {
    setModalState(prev => ({
      ...prev,
      showPlanModal: true,
      selectedPlan: plan || null
    }));
  };

  const closePlanModal = () => {
    setModalState(prev => ({
      ...prev,
      showPlanModal: false,
      selectedPlan: null
    }));
  };

  const openSubscriptionModal = (subscription?: UserSubscription) => {
    setModalState(prev => ({
      ...prev,
      showSubscriptionModal: true,
      selectedSubscription: subscription || null
    }));
  };

  const closeSubscriptionModal = () => {
    setModalState(prev => ({
      ...prev,
      showSubscriptionModal: false,
      selectedSubscription: null
    }));
  };

  // Filtered data
  const filteredSubscriptions = subscriptionsHook.subscriptions.filter(subscription => {
    const matchesSearch = !filterState.searchTerm || 
      subscription.username.toLowerCase().includes(filterState.searchTerm.toLowerCase()) ||
      subscription.email.toLowerCase().includes(filterState.searchTerm.toLowerCase());
    
    const matchesStatus = filterState.filterStatus === 'all' || 
      subscription.status === filterState.filterStatus;
    
    const matchesPlan = filterState.filterPlan === 'all' || 
      subscription.planId === filterState.filterPlan;

    return matchesSearch && matchesStatus && matchesPlan;
  });

  // Loading state
  const isLoading = plansHook.loading || subscriptionsHook.loading || 
                   transactionsHook.loading || statsHook.loading;

  // Error state
  const hasError = plansHook.error || subscriptionsHook.error || 
                  transactionsHook.error || statsHook.error;

  // Refresh all data
  const refreshAll = async () => {
    await Promise.all([
      plansHook.refreshPlans(),
      subscriptionsHook.refreshSubscriptions(),
      transactionsHook.refreshTransactions(),
      statsHook.refreshStats()
    ]);
  };

  return {
    // Tab state
    activeTab,
    setActiveTab,

    // Filter state
    filterState,
    updateFilter,
    resetFilters,

    // Modal state
    modalState,
    openPlanModal,
    closePlanModal,
    openSubscriptionModal,
    closeSubscriptionModal,

    // Data
    plans: plansHook.plans,
    subscriptions: subscriptionsHook.subscriptions,
    filteredSubscriptions,
    transactions: transactionsHook.transactions,
    stats: statsHook.stats,

    // Loading and error states
    isLoading,
    hasError,
    errors: {
      plans: plansHook.error,
      subscriptions: subscriptionsHook.error,
      transactions: transactionsHook.error,
      stats: statsHook.error
    },

    // Actions
    planActions: {
      create: plansHook.createPlan,
      update: plansHook.updatePlan,
      delete: plansHook.deletePlan,
      refresh: plansHook.refreshPlans
    },
    subscriptionActions: {
      update: subscriptionsHook.updateSubscription,
      cancel: subscriptionsHook.cancelSubscription,
      renew: subscriptionsHook.renewSubscription,
      refresh: subscriptionsHook.refreshSubscriptions
    },
    transactionActions: {
      processRefund: transactionsHook.processRefund,
      retryPayment: transactionsHook.retryPayment,
      refresh: transactionsHook.refreshTransactions
    },
    statsActions: {
      refresh: statsHook.refreshStats
    },

    // Global actions
    refreshAll
  };
}
