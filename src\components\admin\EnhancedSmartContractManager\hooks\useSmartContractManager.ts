import { useState, useEffect, useCallback } from 'react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { NetworkInfo, SmartContractManagerHook } from '../types';

export function useSmartContractManager(): SmartContractManagerHook {
  const { t } = useAdminTranslation();
  
  // Tab state
  const [activeTab, setActiveTab] = useState('verification');
  
  // Global state
  const [supportedNetworks, setSupportedNetworks] = useState<NetworkInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Load supported networks on component mount
  useEffect(() => {
    loadSupportedNetworks();
  }, [loadSupportedNetworks]);

  /**
   * تحميل الشبكات المدعومة
   */
  const loadSupportedNetworks = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('🔄 Loading supported networks...');
      // استخدام PHP API مباشرة كحل مؤقت
      const apiUrl = window.location.origin.includes('localhost:3000')
        ? '/api/enhanced-contracts/contract-verification?action=supported-networks'
        : '/ikaros-p2p/api/enhanced-contracts/contract-verification.php?action=supported-networks';

      console.log('📡 API URL:', apiUrl);
      const response = await fetch(apiUrl);

      console.log('📡 Response status:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      console.log('📡 Response data:', data);

      if (data.success) {
        setSupportedNetworks(data.data || []);
        console.log('✅ Supported networks loaded:', data.data?.length || 0);
      } else {
        throw new Error(data.error || t('networks.loadFailed'));
      }
    } catch (error) {
      console.error('❌ Failed to load supported networks:', error);
      setError(error instanceof Error ? error.message : t('networks.loadFailed'));
    } finally {
      setIsLoading(false);
    }
  }, [t]);

  // Stable callback functions
  const handleSetError = useCallback((error: string | null) => {
    setError(error);
  }, []);

  const handleSetSuccess = useCallback((success: string | null) => {
    setSuccess(success);
  }, []);

  const handleSetIsLoading = useCallback((loading: boolean) => {
    setIsLoading(loading);
  }, []);

  return {
    // Tab state
    activeTab,
    setActiveTab,

    // Global state
    supportedNetworks,
    isLoading,
    error,
    success,

    // Actions
    loadSupportedNetworks,
    setError: handleSetError,
    setSuccess: handleSetSuccess,
    setIsLoading: handleSetIsLoading
  };
}
