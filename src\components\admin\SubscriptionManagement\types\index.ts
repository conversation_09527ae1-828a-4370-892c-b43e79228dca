// Types for Subscription Management System

export interface SubscriptionPlan {
  id: string;
  name: string;
  type: 'free' | 'basic' | 'pro' | 'enterprise';
  price: number;
  currency: string;
  billingCycle: 'monthly' | 'yearly';
  features: {
    offersLimit: number;
    commission: number;
    support: string;
    analytics: boolean;
    priority: number;
    apiAccess: boolean;
    escrowFree: boolean;
    whiteLabel?: boolean;
    dedicatedManager?: boolean;
  };
  isActive: boolean;
  userCount: number;
  revenue: number;
  createdAt: string;
  updatedAt: string;
}

export interface UserSubscription {
  id: string;
  userId: string;
  username: string;
  email: string;
  planId: string;
  planName: string;
  status: 'active' | 'expired' | 'cancelled' | 'pending';
  startDate: string;
  endDate: string;
  autoRenew: boolean;
  paymentMethod: string;
  totalPaid: number;
  offersUsed: number;
  offersLimit: number;
  isVerified: boolean;
  verificationLevel: number;
  lastActivity: string;
}

export interface PaymentTransaction {
  id: string;
  userId: string;
  username: string;
  planId: string;
  planName: string;
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  paymentMethod: string;
  transactionId: string;
  createdAt: string;
  processedAt?: string;
  failureReason?: string;
}

export interface SubscriptionStats {
  totalRevenue: number;
  monthlyRevenue: number;
  activeSubscriptions: number;
  freeUsers: number;
  paidUsers: number;
  conversionRate: number;
  churnRate: number;
  averageRevenue: number;
  lifetimeValue: number;
  monthlyGrowth: number;
}

export interface SubscriptionManagementProps {
  className?: string;
}

export interface TabConfig {
  id: string;
  label: string;
  icon: any;
}

export interface FilterState {
  searchTerm: string;
  filterStatus: string;
  filterPlan: string;
}

export interface ModalState {
  showPlanModal: boolean;
  showSubscriptionModal: boolean;
  selectedPlan: SubscriptionPlan | null;
  selectedSubscription: UserSubscription | null;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Hook Return Types
export interface UseSubscriptionPlansReturn {
  plans: SubscriptionPlan[];
  loading: boolean;
  error: string | null;
  createPlan: (plan: Partial<SubscriptionPlan>) => Promise<void>;
  updatePlan: (id: string, plan: Partial<SubscriptionPlan>) => Promise<void>;
  deletePlan: (id: string) => Promise<void>;
  refreshPlans: () => Promise<void>;
}

export interface UseUserSubscriptionsReturn {
  subscriptions: UserSubscription[];
  loading: boolean;
  error: string | null;
  updateSubscription: (id: string, subscription: Partial<UserSubscription>) => Promise<void>;
  cancelSubscription: (id: string) => Promise<void>;
  renewSubscription: (id: string) => Promise<void>;
  refreshSubscriptions: () => Promise<void>;
}

export interface UsePaymentTransactionsReturn {
  transactions: PaymentTransaction[];
  loading: boolean;
  error: string | null;
  processRefund: (id: string) => Promise<void>;
  retryPayment: (id: string) => Promise<void>;
  refreshTransactions: () => Promise<void>;
}

export interface UseSubscriptionStatsReturn {
  stats: SubscriptionStats | null;
  loading: boolean;
  error: string | null;
  refreshStats: () => Promise<void>;
}

// Component Props Types
export interface OverviewTabProps {
  stats: SubscriptionStats | null;
  plans: SubscriptionPlan[];
}

export interface PlansTabProps {
  plans: SubscriptionPlan[];
  onCreatePlan: () => void;
  onEditPlan: (plan: SubscriptionPlan) => void;
  onDeletePlan: (id: string) => void;
}

export interface SubscriptionsTabProps {
  subscriptions: UserSubscription[];
  filterState: FilterState;
  onFilterChange: (filters: Partial<FilterState>) => void;
  onEditSubscription: (subscription: UserSubscription) => void;
  onCancelSubscription: (id: string) => void;
}

export interface PaymentsTabProps {
  transactions: PaymentTransaction[];
  onProcessRefund: (id: string) => void;
  onRetryPayment: (id: string) => void;
}

export interface VerificationTabProps {
  subscriptions: UserSubscription[];
}

export interface AnalyticsTabProps {
  stats: SubscriptionStats | null;
  plans: SubscriptionPlan[];
  transactions: PaymentTransaction[];
}

export interface SettingsTabProps {
  onSaveSettings: (settings: any) => void;
}

// Utility Types
export type PlanType = SubscriptionPlan['type'];
export type SubscriptionStatus = UserSubscription['status'];
export type PaymentStatus = PaymentTransaction['status'];
export type BillingCycle = SubscriptionPlan['billingCycle'];
