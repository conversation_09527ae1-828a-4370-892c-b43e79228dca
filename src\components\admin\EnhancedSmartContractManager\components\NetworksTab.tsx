'use client';

import React, { useEffect } from 'react';
import { Network, Plus, Edit, Trash2, Globe, Activity } from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { useNetworkManagement } from '../hooks';
import { NetworkModal } from './';

interface NetworksTabProps {
  onError: (error: string) => void;
  onSuccess: (message: string) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

export default function NetworksTab({ 
  onError, 
  onSuccess, 
  isLoading, 
  setIsLoading 
}: NetworksTabProps) {
  const { t, isRTL } = useAdminTranslation();

  const {
    networksList,
    showNetworkModal,
    editingNetwork,
    networkForm,
    loadNetworksList,
    handleAddNetwork,
    handleUpdateNetwork,
    handleDeleteNetwork,
    openNetworkModal,
    closeNetworkModal,
    editNetwork,
    updateNetworkForm
  } = useNetworkManagement();

  // Load networks on mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        await loadNetworksList();
      } catch (error) {
        onError(error instanceof Error ? error.message : t('networks.loadFailed'));
      } finally {
        setIsLoading(false);
      }
    };
    
    loadData();
  }, []);

  const handleAdd = async () => {
    try {
      setIsLoading(true);
      await handleAddNetwork();
      onSuccess(t('networks.addSuccess'));
    } catch (error) {
      onError(error instanceof Error ? error.message : t('networks.addFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdate = async () => {
    try {
      setIsLoading(true);
      await handleUpdateNetwork();
      onSuccess(t('networks.updateSuccess'));
    } catch (error) {
      onError(error instanceof Error ? error.message : t('networks.updateFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm(t('networks.confirmDelete'))) return;
    
    try {
      setIsLoading(true);
      await handleDeleteNetwork(id);
      onSuccess(t('networks.deleteSuccess'));
    } catch (error) {
      onError(error instanceof Error ? error.message : t('networks.deleteFailed'));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-200 dark:border-gray-700 ${isRTL ? 'text-right' : 'text-left'}`}>
      <div className={`flex items-center justify-between mb-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
          {t('networks.management')}
        </h2>
        <button
          onClick={openNetworkModal}
          disabled={isLoading}
          className={`flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg font-medium transition-colors ${isRTL ? 'flex-row-reverse' : ''}`}
        >
          <Plus className="w-4 h-4" />
          {t('networks.addNetwork')}
        </button>
      </div>

      {/* Networks List */}
      <div className="space-y-4">
        {networksList.length === 0 ? (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            {isLoading ? t('common.loading') : t('networks.noNetworks')}
          </div>
        ) : (
          networksList.map((network) => (
            <div
              key={network.id}
              className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 border border-gray-200 dark:border-gray-600"
            >
              <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
                <div className={`flex items-center gap-3 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    <Network className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    <h3 className="font-semibold text-gray-900 dark:text-white">
                      {network.name}
                    </h3>
                  </div>
                  
                  <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                    {network.is_testnet && (
                      <span className="px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded text-xs">
                        {t('networks.testnet')}
                      </span>
                    )}
                    
                    <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                      <Activity className={`w-3 h-3 ${network.is_active ? 'text-green-600' : 'text-red-600'}`} />
                      <span className={`text-xs ${network.is_active ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                        {network.is_active ? t('networks.active') : t('networks.inactive')}
                      </span>
                    </div>
                  </div>
                </div>

                <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <button
                    onClick={() => editNetwork(network)}
                    disabled={isLoading}
                    className="p-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-colors"
                    title={t('common.actions.edit')}
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  
                  <button
                    onClick={() => handleDelete(network.id)}
                    disabled={isLoading}
                    className="p-2 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors"
                    title={t('common.actions.delete')}
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div className="mt-3 grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('networks.chainId')}:
                  </span>
                  <span className="ml-2 font-mono text-gray-900 dark:text-white">
                    {network.chain_id}
                  </span>
                </div>
                
                <div>
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('networks.currency')}:
                  </span>
                  <span className="ml-2 text-gray-900 dark:text-white">
                    {network.currency_symbol}
                  </span>
                </div>
                
                <div className={`flex items-center gap-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
                  <Globe className="w-4 h-4 text-gray-400" />
                  <a
                    href={network.explorer_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 dark:text-blue-400 hover:underline"
                  >
                    {t('networks.explorer')}
                  </a>
                </div>
              </div>

              {network.rpc_url && (
                <div className="mt-2 text-sm">
                  <span className="text-gray-600 dark:text-gray-400">
                    {t('networks.rpcUrl')}:
                  </span>
                  <span className="ml-2 font-mono text-gray-900 dark:text-white text-xs break-all">
                    {network.rpc_url}
                  </span>
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Network Modal */}
      {showNetworkModal && (
        <NetworkModal
          isOpen={showNetworkModal}
          onClose={closeNetworkModal}
          onSave={editingNetwork ? handleUpdate : handleAdd}
          editingNetwork={editingNetwork}
          networkForm={networkForm}
          updateNetworkForm={updateNetworkForm}
          isLoading={isLoading}
          isRTL={isRTL}
        />
      )}
    </div>
  );
}
