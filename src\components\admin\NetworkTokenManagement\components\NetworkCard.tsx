'use client';

import React from 'react';
import {
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Eye,
  Settings,
  Wifi,
  WifiOff,
  Globe,
  TestTube,
  Activity,
  TrendingUp,
  ExternalLink,
  Copy,
  Power,
  PowerOff
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { NetworkCardProps } from '../types';

export default function NetworkCard({
  network,
  isSelected,
  onToggle,
  onAction,
  onSelectionChange
}: NetworkCardProps) {
  const { t, isRTL, formatNumber, formatRelativeTime } = useAdminTranslation();

  // أيقونة الحالة
  const getStatusIcon = () => {
    switch (network.status) {
      case 'active':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'inactive':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'maintenance':
        return <Settings className="w-5 h-5 text-yellow-500" />;
      case 'error':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      case 'syncing':
        return <Clock className="w-5 h-5 text-blue-500 animate-spin" />;
      default:
        return <XCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  // لون الحالة
  const getStatusColor = () => {
    switch (network.status) {
      case 'active':
        return 'text-green-600 bg-green-50 dark:bg-green-900/20 dark:text-green-400';
      case 'inactive':
        return 'text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400';
      case 'maintenance':
        return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'error':
        return 'text-red-600 bg-red-50 dark:bg-red-900/20 dark:text-red-400';
      case 'syncing':
        return 'text-blue-600 bg-blue-50 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'text-gray-600 bg-gray-50 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  // نسخ العنوان
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // يمكن إضافة toast notification هنا
  };

  return (
    <div className="px-6 py-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors">
      <div className={`flex items-center justify-between ${isRTL ? 'flex-row-reverse' : ''}`}>
        {/* معلومات الشبكة الأساسية */}
        <div className={`flex items-center flex-1 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {/* Checkbox */}
          <input
            type="checkbox"
            checked={isSelected}
            onChange={(e) => onSelectionChange(network.id, e.target.checked)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />

          {/* أيقونة النوع */}
          <div className={`${isRTL ? 'mr-4' : 'ml-4'}`}>
            {network.type === 'testnet' ? (
              <TestTube className="w-8 h-8 text-orange-500" />
            ) : (
              <Globe className="w-8 h-8 text-blue-500" />
            )}
          </div>

          {/* تفاصيل الشبكة */}
          <div className={`flex-1 ${isRTL ? 'mr-4 text-right' : 'ml-4 text-left'}`}>
            <div className="flex items-center gap-2">
              <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                {network.name}
              </h4>
              <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor()}`}>
                {t(`networks.status.${network.status}`)}
              </span>
              {network.type === 'testnet' && (
                <span className="px-2 py-1 text-xs font-medium rounded-full bg-orange-100 text-orange-600 dark:bg-orange-900/20 dark:text-orange-400">
                  {t('networks.testnet')}
                </span>
              )}
            </div>

            <div className="mt-1 space-y-1">
              <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                <span>Chain ID: {network.chainId}</span>
                <span>{network.nativeCurrency.symbol}</span>
                <span>{t('networks.blockTime')}: {network.blockTime}s</span>
                <span>{t('networks.gasPrice')}: {network.gasPrice} Gwei</span>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-500">
                <div className="flex items-center gap-1">
                  <Activity className="w-4 h-4" />
                  <span>{t('networks.uptime')}: {network.uptime}%</span>
                </div>
                <div className="flex items-center gap-1">
                  <TrendingUp className="w-4 h-4" />
                  <span>{t('networks.latency')}: {network.latency}ms</span>
                </div>
                <div className="flex items-center gap-1">
                  <span>{t('networks.blockHeight')}: {formatNumber(network.blockHeight)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* إحصائيات سريعة */}
        <div className={`hidden lg:flex items-center gap-6 ${isRTL ? 'flex-row-reverse' : ''}`}>
          <div className={`text-center ${isRTL ? 'text-right' : 'text-left'}`}>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {formatNumber(network.dailyTransactions)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t('networks.dailyTx')}
            </div>
          </div>
          
          <div className={`text-center ${isRTL ? 'text-right' : 'text-left'}`}>
            <div className="text-sm font-medium text-gray-900 dark:text-white">
              {formatNumber(network.nodeCount)}
            </div>
            <div className="text-xs text-gray-500 dark:text-gray-400">
              {t('networks.nodes')}
            </div>
          </div>
        </div>

        {/* حالة الاتصال */}
        <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse' : ''}`}>
          {network.status === 'active' ? (
            <Wifi className="w-5 h-5 text-green-500" />
          ) : (
            <WifiOff className="w-5 h-5 text-red-500" />
          )}
          {getStatusIcon()}
        </div>

        {/* أزرار الإجراءات */}
        <div className={`flex items-center gap-2 ${isRTL ? 'flex-row-reverse mr-4' : 'ml-4'}`}>
          {/* زر التفعيل/الإلغاء */}
          <button
            onClick={() => onToggle(network.id)}
            className={`p-2 rounded-lg transition-colors ${
              network.isEnabled
                ? 'bg-green-100 text-green-600 hover:bg-green-200 dark:bg-green-900/20 dark:text-green-400'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400'
            }`}
            title={network.isEnabled ? t('common.disable') : t('common.enable')}
          >
            {network.isEnabled ? (
              <Power className="w-4 h-4" />
            ) : (
              <PowerOff className="w-4 h-4" />
            )}
          </button>

          {/* زر نسخ RPC URL */}
          <button
            onClick={() => copyToClipboard(network.rpcUrl)}
            className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 transition-colors"
            title={t('networks.copyRpcUrl')}
          >
            <Copy className="w-4 h-4" />
          </button>

          {/* زر فتح Explorer */}
          <button
            onClick={() => window.open(network.explorerUrl, '_blank')}
            className="p-2 rounded-lg bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-400 transition-colors"
            title={t('networks.openExplorer')}
          >
            <ExternalLink className="w-4 h-4" />
          </button>

          {/* زر عرض التفاصيل */}
          <button
            onClick={() => onAction('view', network.id)}
            className="p-2 rounded-lg bg-blue-100 text-blue-600 hover:bg-blue-200 dark:bg-blue-900/20 dark:text-blue-400 transition-colors"
            title={t('common.viewDetails')}
          >
            <Eye className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* معلومات إضافية للشاشات الصغيرة */}
      <div className="lg:hidden mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500 dark:text-gray-400">{t('networks.dailyTx')}:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {formatNumber(network.dailyTransactions)}
            </span>
          </div>
          <div>
            <span className="text-gray-500 dark:text-gray-400">{t('networks.nodes')}:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {formatNumber(network.nodeCount)}
            </span>
          </div>
          <div>
            <span className="text-gray-500 dark:text-gray-400">{t('networks.uptime')}:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {network.uptime}%
            </span>
          </div>
          <div>
            <span className="text-gray-500 dark:text-gray-400">{t('networks.lastSync')}:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">
              {formatRelativeTime(network.lastSync)}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
