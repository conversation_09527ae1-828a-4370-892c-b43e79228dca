'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp, TrendingDown, DollarSign, Users } from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { AnalyticsTabProps } from '../types';

export function AnalyticsTab({ stats, plans, transactions }: AnalyticsTabProps) {
  const { t, formatCurrency, formatNumber, getDirectionClasses } = useAdminTranslation();
  const dirClasses = getDirectionClasses();

  if (!stats) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const revenueByPlan = plans.map(plan => ({
    name: plan.name,
    revenue: plan.revenue,
    users: plan.userCount,
    percentage: (plan.revenue / stats.totalRevenue) * 100
  }));

  const monthlyTransactions = transactions.filter(t => {
    const transactionDate = new Date(t.createdAt);
    const currentMonth = new Date();
    return transactionDate.getMonth() === currentMonth.getMonth() &&
           transactionDate.getFullYear() === currentMonth.getFullYear();
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          {t('subscriptions.analytics.title')}
        </h2>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {t('subscriptions.analytics.description')}
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('subscriptions.analytics.conversionRate')}
              </p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400 mt-2">
                {formatNumber(stats.conversionRate)}%
              </p>
            </div>
            <div className="bg-blue-500 p-3 rounded-lg">
              <TrendingUp className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('subscriptions.analytics.churnRate')}
              </p>
              <p className="text-2xl font-bold text-red-600 dark:text-red-400 mt-2">
                {formatNumber(stats.churnRate)}%
              </p>
            </div>
            <div className="bg-red-500 p-3 rounded-lg">
              <TrendingDown className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('subscriptions.analytics.averageRevenue')}
              </p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400 mt-2">
                {formatCurrency(stats.averageRevenue)}
              </p>
            </div>
            <div className="bg-green-500 p-3 rounded-lg">
              <DollarSign className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                {t('subscriptions.analytics.lifetimeValue')}
              </p>
              <p className="text-2xl font-bold text-purple-600 dark:text-purple-400 mt-2">
                {formatCurrency(stats.lifetimeValue)}
              </p>
            </div>
            <div className="bg-purple-500 p-3 rounded-lg">
              <Users className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Revenue by Plan */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('subscriptions.analytics.revenueByPlan')}
          </h3>
          <PieChart className="h-5 w-5 text-gray-400" />
        </div>
        <div className="space-y-4">
          {revenueByPlan.map((plan, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <div className={`w-4 h-4 rounded-full ${
                  index === 0 ? 'bg-blue-500' :
                  index === 1 ? 'bg-green-500' :
                  index === 2 ? 'bg-purple-500' : 'bg-orange-500'
                }`}></div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {plan.name}
                </span>
              </div>
              <div className="text-right">
                <div className="text-sm font-bold text-gray-900 dark:text-white">
                  {formatCurrency(plan.revenue)}
                </div>
                <div className="text-xs text-gray-500">
                  {formatNumber(plan.percentage)}%
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Monthly Performance */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('subscriptions.analytics.monthlyGrowth')}
            </h3>
            <LineChart className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('subscriptions.analytics.revenueGrowth')}
              </span>
              <span className="text-sm font-medium text-green-600 dark:text-green-400">
                +{formatNumber(stats.monthlyGrowth)}%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('subscriptions.analytics.userGrowth')}
              </span>
              <span className="text-sm font-medium text-blue-600 dark:text-blue-400">
                +{formatNumber(stats.monthlyGrowth * 0.8)}%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('subscriptions.analytics.subscriptionGrowth')}
              </span>
              <span className="text-sm font-medium text-purple-600 dark:text-purple-400">
                +{formatNumber(stats.monthlyGrowth * 1.2)}%
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('subscriptions.analytics.monthlyTransactions')}
            </h3>
            <BarChart3 className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('subscriptions.analytics.totalTransactions')}
              </span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {monthlyTransactions.length}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('subscriptions.analytics.successfulTransactions')}
              </span>
              <span className="text-sm font-medium text-green-600 dark:text-green-400">
                {monthlyTransactions.filter(t => t.status === 'completed').length}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('subscriptions.analytics.failedTransactions')}
              </span>
              <span className="text-sm font-medium text-red-600 dark:text-red-400">
                {monthlyTransactions.filter(t => t.status === 'failed').length}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
