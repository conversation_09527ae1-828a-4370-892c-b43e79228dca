import { useState, useCallback } from 'react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { NetworkFormData, NetworkManagementHook } from '../types';

export function useNetworkManagement(): NetworkManagementHook {
  const { t } = useAdminTranslation();
  
  // State
  const [networksList, setNetworksList] = useState<any[]>([]);
  const [showNetworkModal, setShowNetworkModal] = useState(false);
  const [editingNetwork, setEditingNetwork] = useState<any>(null);
  const [networkForm, setNetworkForm] = useState<NetworkFormData>({
    name: '',
    chain_id: '',
    rpc_url: '',
    explorer_url: '',
    native_currency: '',
    currency_symbol: '',
    currency_decimals: '18',
    is_testnet: false,
    is_active: true,
    gas_price_gwei: '20',
    block_time_seconds: '15',
    confirmation_blocks: '12'
  });

  /**
   * تحميل قائمة الشبكات
   */
  const loadNetworksList = useCallback(async () => {
    try {
      const response = await fetch('/api/enhanced-contracts/network-management?action=list');
      const data = await response.json();

      if (data.success) {
        setNetworksList(data.data || []);
      } else {
        throw new Error(data.error || t('networks.loadFailed'));
      }
    } catch (error) {
      console.error('Failed to load networks:', error);
      throw error;
    }
  }, [t]);

  /**
   * إضافة شبكة جديدة
   */
  const handleAddNetwork = useCallback(async () => {
    try {
      const response = await fetch('/api/enhanced-contracts/network-management?action=add', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(networkForm)
      });

      const data = await response.json();
      if (data.success) {
        setShowNetworkModal(false);
        resetNetworkForm();
        await loadNetworksList();
      } else {
        throw new Error(data.error || t('networks.addFailed'));
      }
    } catch (error) {
      console.error('Failed to add network:', error);
      throw error;
    }
  }, [networkForm, loadNetworksList, t]);

  /**
   * تحديث شبكة موجودة
   */
  const handleUpdateNetwork = async () => {
    if (!editingNetwork) return;

    try {
      const response = await fetch(`/api/enhanced-contracts/network-management?action=update&id=${editingNetwork.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(networkForm)
      });

      const data = await response.json();
      if (data.success) {
        setShowNetworkModal(false);
        setEditingNetwork(null);
        resetNetworkForm();
        await loadNetworksList();
      } else {
        throw new Error(data.error || t('networks.updateFailed'));
      }
    } catch (error) {
      console.error('Failed to update network:', error);
      throw error;
    }
  };

  /**
   * حذف شبكة
   */
  const handleDeleteNetwork = async (id: number) => {
    try {
      const response = await fetch(`/api/enhanced-contracts/network-management?action=delete&id=${id}`, {
        method: 'DELETE'
      });

      const data = await response.json();
      if (data.success) {
        await loadNetworksList();
      } else {
        throw new Error(data.error || t('networks.deleteFailed'));
      }
    } catch (error) {
      console.error('Failed to delete network:', error);
      throw error;
    }
  };

  /**
   * فتح مودال الشبكة
   */
  const openNetworkModal = () => {
    setShowNetworkModal(true);
  };

  /**
   * إغلاق مودال الشبكة
   */
  const closeNetworkModal = () => {
    setShowNetworkModal(false);
    setEditingNetwork(null);
    resetNetworkForm();
  };

  /**
   * تعديل شبكة
   */
  const editNetwork = (network: any) => {
    setEditingNetwork(network);
    setNetworkForm({
      name: network.name || '',
      chain_id: network.chain_id?.toString() || '',
      rpc_url: network.rpc_url || '',
      explorer_url: network.explorer_url || '',
      native_currency: network.native_currency || '',
      currency_symbol: network.currency_symbol || '',
      currency_decimals: network.currency_decimals?.toString() || '18',
      is_testnet: network.is_testnet || false,
      is_active: network.is_active !== false,
      gas_price_gwei: network.gas_price_gwei?.toString() || '20',
      block_time_seconds: network.block_time_seconds?.toString() || '15',
      confirmation_blocks: network.confirmation_blocks?.toString() || '12'
    });
    setShowNetworkModal(true);
  };

  /**
   * إعادة تعيين نموذج الشبكة
   */
  const resetNetworkForm = useCallback(() => {
    setNetworkForm({
      name: '',
      chain_id: '',
      rpc_url: '',
      explorer_url: '',
      native_currency: '',
      currency_symbol: '',
      currency_decimals: '18',
      is_testnet: false,
      is_active: true,
      gas_price_gwei: '20',
      block_time_seconds: '15',
      confirmation_blocks: '12'
    });
  }, []);

  /**
   * تحديث حقل في نموذج الشبكة
   */
  const updateNetworkForm = (field: string, value: any) => {
    setNetworkForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return {
    // State
    networksList,
    showNetworkModal,
    editingNetwork,
    networkForm,

    // Actions
    loadNetworksList,
    handleAddNetwork,
    handleUpdateNetwork,
    handleDeleteNetwork,
    openNetworkModal,
    closeNetworkModal,
    editNetwork,
    resetNetworkForm,
    updateNetworkForm
  };
}
