'use client';

import React, { useState } from 'react';
import { Settings, Save, RefreshCw, Bell, CreditCard, Shield, Globe, Mail } from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { SettingsTabProps } from '../types';

export function SettingsTab({ onSaveSettings }: SettingsTabProps) {
  const { t, getDirectionClasses } = useAdminTranslation();
  const dirClasses = getDirectionClasses();

  const [settings, setSettings] = useState({
    // Payment Settings
    defaultCurrency: 'USD',
    paymentMethods: {
      creditCard: true,
      paypal: true,
      cryptocurrency: false,
      bankTransfer: true
    },
    
    // Subscription Settings
    autoRenewal: true,
    gracePeriod: 7, // days
    trialPeriod: 14, // days
    
    // Notification Settings
    emailNotifications: {
      paymentSuccess: true,
      paymentFailed: true,
      subscriptionExpiring: true,
      subscriptionExpired: true,
      planUpgrade: true
    },
    
    // Security Settings
    requireVerification: true,
    minimumVerificationLevel: 2,
    
    // Commission Settings
    defaultCommission: 1.5,
    freeUserCommission: 2.0,
    
    // System Settings
    maintenanceMode: false,
    debugMode: false
  });

  const handleSave = () => {
    onSaveSettings(settings);
  };

  const handleReset = () => {
    // Reset to default values
    setSettings({
      defaultCurrency: 'USD',
      paymentMethods: {
        creditCard: true,
        paypal: true,
        cryptocurrency: false,
        bankTransfer: true
      },
      autoRenewal: true,
      gracePeriod: 7,
      trialPeriod: 14,
      emailNotifications: {
        paymentSuccess: true,
        paymentFailed: true,
        subscriptionExpiring: true,
        subscriptionExpired: true,
        planUpgrade: true
      },
      requireVerification: true,
      minimumVerificationLevel: 2,
      defaultCommission: 1.5,
      freeUserCommission: 2.0,
      maintenanceMode: false,
      debugMode: false
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            {t('subscriptions.settings.title')}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('subscriptions.settings.description')}
          </p>
        </div>
        <div className="flex space-x-2 rtl:space-x-reverse">
          <button
            onClick={handleReset}
            className="flex items-center space-x-2 rtl:space-x-reverse bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg transition-colors"
          >
            <RefreshCw className="h-4 w-4" />
            <span>{t('common.reset')}</span>
          </button>
          <button
            onClick={handleSave}
            className="flex items-center space-x-2 rtl:space-x-reverse bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <Save className="h-4 w-4" />
            <span>{t('common.save')}</span>
          </button>
        </div>
      </div>

      {/* Payment Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
          <CreditCard className="h-5 w-5 text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('subscriptions.settings.paymentSettings')}
          </h3>
        </div>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('subscriptions.settings.defaultCurrency')}
            </label>
            <select
              value={settings.defaultCurrency}
              onChange={(e) => setSettings(prev => ({ ...prev, defaultCurrency: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="USD">USD - US Dollar</option>
              <option value="EUR">EUR - Euro</option>
              <option value="GBP">GBP - British Pound</option>
              <option value="SAR">SAR - Saudi Riyal</option>
              <option value="AED">AED - UAE Dirham</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('subscriptions.settings.enabledPaymentMethods')}
            </label>
            <div className="space-y-2">
              {Object.entries(settings.paymentMethods).map(([method, enabled]) => (
                <label key={method} className="flex items-center space-x-3 rtl:space-x-reverse">
                  <input
                    type="checkbox"
                    checked={enabled}
                    onChange={(e) => setSettings(prev => ({
                      ...prev,
                      paymentMethods: {
                        ...prev.paymentMethods,
                        [method]: e.target.checked
                      }
                    }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm text-gray-700 dark:text-gray-300">
                    {t(`subscriptions.settings.paymentMethods.${method}`)}
                  </span>
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Subscription Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
          <Settings className="h-5 w-5 text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('subscriptions.settings.subscriptionSettings')}
          </h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="flex items-center space-x-3 rtl:space-x-reverse">
              <input
                type="checkbox"
                checked={settings.autoRenewal}
                onChange={(e) => setSettings(prev => ({ ...prev, autoRenewal: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {t('subscriptions.settings.enableAutoRenewal')}
              </span>
            </label>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('subscriptions.settings.gracePeriod')}
            </label>
            <input
              type="number"
              value={settings.gracePeriod}
              onChange={(e) => setSettings(prev => ({ ...prev, gracePeriod: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              min="1"
              max="30"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('subscriptions.settings.trialPeriod')}
            </label>
            <input
              type="number"
              value={settings.trialPeriod}
              onChange={(e) => setSettings(prev => ({ ...prev, trialPeriod: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
              min="0"
              max="90"
            />
          </div>
        </div>
      </div>

      {/* Notification Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
          <Bell className="h-5 w-5 text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('subscriptions.settings.notificationSettings')}
          </h3>
        </div>
        <div className="space-y-3">
          {Object.entries(settings.emailNotifications).map(([notification, enabled]) => (
            <label key={notification} className="flex items-center space-x-3 rtl:space-x-reverse">
              <input
                type="checkbox"
                checked={enabled}
                onChange={(e) => setSettings(prev => ({
                  ...prev,
                  emailNotifications: {
                    ...prev.emailNotifications,
                    [notification]: e.target.checked
                  }
                }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {t(`subscriptions.settings.notifications.${notification}`)}
              </span>
            </label>
          ))}
        </div>
      </div>

      {/* Security Settings */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div className="flex items-center space-x-3 rtl:space-x-reverse mb-4">
          <Shield className="h-5 w-5 text-gray-400" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('subscriptions.settings.securitySettings')}
          </h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="flex items-center space-x-3 rtl:space-x-reverse">
              <input
                type="checkbox"
                checked={settings.requireVerification}
                onChange={(e) => setSettings(prev => ({ ...prev, requireVerification: e.target.checked }))}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="text-sm text-gray-700 dark:text-gray-300">
                {t('subscriptions.settings.requireVerification')}
              </span>
            </label>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {t('subscriptions.settings.minimumVerificationLevel')}
            </label>
            <select
              value={settings.minimumVerificationLevel}
              onChange={(e) => setSettings(prev => ({ ...prev, minimumVerificationLevel: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value={1}>{t('subscriptions.verification.basic')}</option>
              <option value={2}>{t('subscriptions.verification.email')}</option>
              <option value={3}>{t('subscriptions.verification.phone')}</option>
              <option value={4}>{t('subscriptions.verification.identity')}</option>
              <option value={5}>{t('subscriptions.verification.premium')}</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );
}
