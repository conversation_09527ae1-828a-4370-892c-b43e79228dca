'use client';

import React from 'react';
import {
  DollarSign,
  Users,
  TrendingUp,
  CreditCard,
  Crown,
  Gift,
  BarChart3,
  ArrowUpCircle,
  ArrowDownCircle,
  Activity
} from 'lucide-react';
import { useAdminTranslation } from '@/hooks/useAdminTranslation';
import { OverviewTabProps } from '../types';

export function OverviewTab({ stats, plans }: OverviewTabProps) {
  const { t, formatCurrency, formatNumber, getDirectionClasses } = useAdminTranslation();
  const dirClasses = getDirectionClasses();

  if (!stats) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const statCards = [
    {
      title: t('subscriptions.overview.totalRevenue'),
      value: formatCurrency(stats.totalRevenue),
      icon: DollarSign,
      color: 'bg-green-500',
      change: `+${formatNumber(stats.monthlyGrowth)}%`,
      changeType: 'positive' as const
    },
    {
      title: t('subscriptions.overview.monthlyRevenue'),
      value: formatCurrency(stats.monthlyRevenue),
      icon: TrendingUp,
      color: 'bg-blue-500',
      change: `+${formatNumber(stats.monthlyGrowth)}%`,
      changeType: 'positive' as const
    },
    {
      title: t('subscriptions.overview.activeSubscriptions'),
      value: formatNumber(stats.activeSubscriptions),
      icon: CreditCard,
      color: 'bg-purple-500',
      change: `+${formatNumber(stats.conversionRate)}%`,
      changeType: 'positive' as const
    },
    {
      title: t('subscriptions.overview.totalUsers'),
      value: formatNumber(stats.freeUsers + stats.paidUsers),
      icon: Users,
      color: 'bg-orange-500',
      change: `${formatNumber(stats.churnRate)}%`,
      changeType: 'negative' as const
    }
  ];

  const planStats = plans.map(plan => ({
    name: plan.name,
    users: plan.userCount,
    revenue: plan.revenue,
    type: plan.type
  }));

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat, index) => (
          <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {stat.title}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-2">
                  {stat.value}
                </p>
              </div>
              <div className={`${stat.color} p-3 rounded-lg`}>
                <stat.icon className="h-6 w-6 text-white" />
              </div>
            </div>
            <div className="mt-4 flex items-center">
              {stat.changeType === 'positive' ? (
                <ArrowUpCircle className="h-4 w-4 text-green-500" />
              ) : (
                <ArrowDownCircle className="h-4 w-4 text-red-500" />
              )}
              <span className={`text-sm font-medium ${dirClasses.marginStart} ${
                stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
              }`}>
                {stat.change}
              </span>
              <span className={`text-sm text-gray-500 ${dirClasses.marginStart}`}>
                {t('subscriptions.overview.fromLastMonth')}
              </span>
            </div>
          </div>
        ))}
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Conversion Metrics */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('subscriptions.overview.conversionMetrics')}
            </h3>
            <Activity className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('subscriptions.overview.conversionRate')}
              </span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {formatNumber(stats.conversionRate)}%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('subscriptions.overview.churnRate')}
              </span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {formatNumber(stats.churnRate)}%
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('subscriptions.overview.averageRevenue')}
              </span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {formatCurrency(stats.averageRevenue)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                {t('subscriptions.overview.lifetimeValue')}
              </span>
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                {formatCurrency(stats.lifetimeValue)}
              </span>
            </div>
          </div>
        </div>

        {/* Plan Distribution */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('subscriptions.overview.planDistribution')}
            </h3>
            <BarChart3 className="h-5 w-5 text-gray-400" />
          </div>
          <div className="space-y-3">
            {planStats.map((plan, index) => {
              const percentage = ((plan.users / (stats.freeUsers + stats.paidUsers)) * 100);
              const planIcons = {
                free: Gift,
                basic: CreditCard,
                pro: Crown,
                enterprise: Building
              };
              const PlanIcon = planIcons[plan.type as keyof typeof planIcons] || CreditCard;
              
              return (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <PlanIcon className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {plan.name}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2 rtl:space-x-reverse">
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatNumber(plan.users)}
                    </span>
                    <span className="text-xs text-gray-500">
                      ({formatNumber(percentage)}%)
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* User Distribution */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('subscriptions.overview.userDistribution')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 dark:text-blue-400">
              {formatNumber(stats.freeUsers)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {t('subscriptions.overview.freeUsers')}
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 dark:text-green-400">
              {formatNumber(stats.paidUsers)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {t('subscriptions.overview.paidUsers')}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
